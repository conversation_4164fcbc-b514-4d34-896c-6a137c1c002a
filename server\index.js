import express from 'express'
import cors from 'cors'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import sqlite3 from 'sqlite3'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const app = express()
const PORT = process.env.PORT || 5000
const JWT_SECRET = 'salam-internet-manager-secret-key-2024'

// Middleware
app.use(cors())
app.use(express.json())

// Database setup
const db = new sqlite3.Database(join(__dirname, 'database.db'))

// Initialize database tables
db.serialize(() => {
  // Users table
  db.run(`CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    name TEXT NOT NULL,
    email TEXT,
    role TEXT DEFAULT 'admin',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`)

  // Customers table
  db.run(`CREATE TABLE IF NOT EXISTS customers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    phone TEXT NOT NULL,
    email TEXT,
    address TEXT,
    national_id TEXT UNIQUE,
    status TEXT DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`)

  // Subscription plans table
  db.run(`CREATE TABLE IF NOT EXISTS subscription_plans (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    speed TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    duration_days INTEGER DEFAULT 30,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`)

  // Subscriptions table
  db.run(`CREATE TABLE IF NOT EXISTS subscriptions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    plan_id INTEGER NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status TEXT DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers (id),
    FOREIGN KEY (plan_id) REFERENCES subscription_plans (id)
  )`)

  // Payments table
  db.run(`CREATE TABLE IF NOT EXISTS payments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    subscription_id INTEGER,
    amount DECIMAL(10,2) NOT NULL,
    payment_method TEXT NOT NULL,
    payment_date DATE NOT NULL,
    status TEXT DEFAULT 'completed',
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers (id),
    FOREIGN KEY (subscription_id) REFERENCES subscriptions (id)
  )`)

  // Create default admin user
  const hashedPassword = bcrypt.hashSync('admin123', 10)
  db.run(`INSERT OR IGNORE INTO users (username, password, name, email) 
          VALUES (?, ?, ?, ?)`, 
          ['admin', hashedPassword, 'المدير العام', '<EMAIL>'])

  // Insert sample subscription plans
  db.run(`INSERT OR IGNORE INTO subscription_plans (id, name, speed, price, description) 
          VALUES 
          (1, 'باقة 50 ميجا', '50 Mbps', 100.00, 'باقة اقتصادية للاستخدام المنزلي'),
          (2, 'باقة 100 ميجا', '100 Mbps', 150.00, 'باقة متوسطة مناسبة للعائلات'),
          (3, 'باقة 200 ميجا', '200 Mbps', 250.00, 'باقة عالية السرعة للاستخدام المكثف')`)
})

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.status(401).json({ message: 'Access token required' })
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid or expired token' })
    }
    req.user = user
    next()
  })
}

// Auth routes
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body

  db.get('SELECT * FROM users WHERE username = ?', [username], (err, user) => {
    if (err) {
      return res.status(500).json({ message: 'Database error' })
    }

    if (!user || !bcrypt.compareSync(password, user.password)) {
      return res.status(401).json({ message: 'Invalid credentials' })
    }

    const token = jwt.sign(
      { id: user.id, username: user.username, name: user.name },
      JWT_SECRET,
      { expiresIn: '24h' }
    )

    res.json({
      token,
      user: {
        id: user.id,
        username: user.username,
        name: user.name,
        email: user.email,
        role: user.role
      }
    })
  })
})

app.get('/api/auth/me', authenticateToken, (req, res) => {
  db.get('SELECT id, username, name, email, role FROM users WHERE id = ?', [req.user.id], (err, user) => {
    if (err) {
      return res.status(500).json({ message: 'Database error' })
    }
    if (!user) {
      return res.status(404).json({ message: 'User not found' })
    }
    res.json({ user })
  })
})

// Customers routes
app.get('/api/customers', authenticateToken, (req, res) => {
  db.all('SELECT * FROM customers ORDER BY created_at DESC', (err, customers) => {
    if (err) {
      return res.status(500).json({ message: 'Database error' })
    }
    res.json(customers)
  })
})

app.post('/api/customers', authenticateToken, (req, res) => {
  const { name, phone, email, address, national_id } = req.body

  db.run(
    'INSERT INTO customers (name, phone, email, address, national_id) VALUES (?, ?, ?, ?, ?)',
    [name, phone, email, address, national_id],
    function(err) {
      if (err) {
        return res.status(500).json({ message: 'Database error' })
      }
      res.json({ id: this.lastID, message: 'Customer created successfully' })
    }
  )
})

// Subscriptions routes
app.get('/api/subscriptions', authenticateToken, (req, res) => {
  const query = `
    SELECT s.*, c.name as customer_name, sp.name as plan_name, sp.price
    FROM subscriptions s
    JOIN customers c ON s.customer_id = c.id
    JOIN subscription_plans sp ON s.plan_id = sp.id
    ORDER BY s.created_at DESC
  `
  
  db.all(query, (err, subscriptions) => {
    if (err) {
      return res.status(500).json({ message: 'Database error' })
    }
    res.json(subscriptions)
  })
})

// Payments routes
app.get('/api/payments', authenticateToken, (req, res) => {
  const query = `
    SELECT p.*, c.name as customer_name
    FROM payments p
    JOIN customers c ON p.customer_id = c.id
    ORDER BY p.created_at DESC
  `
  
  db.all(query, (err, payments) => {
    if (err) {
      return res.status(500).json({ message: 'Database error' })
    }
    res.json(payments)
  })
})

// Dashboard stats
app.get('/api/dashboard/stats', authenticateToken, (req, res) => {
  const stats = {}
  
  // Get total customers
  db.get('SELECT COUNT(*) as count FROM customers', (err, result) => {
    if (err) return res.status(500).json({ message: 'Database error' })
    stats.totalCustomers = result.count
    
    // Get active subscriptions
    db.get('SELECT COUNT(*) as count FROM subscriptions WHERE status = "active"', (err, result) => {
      if (err) return res.status(500).json({ message: 'Database error' })
      stats.activeSubscriptions = result.count
      
      // Get expired subscriptions
      db.get('SELECT COUNT(*) as count FROM subscriptions WHERE end_date < date("now")', (err, result) => {
        if (err) return res.status(500).json({ message: 'Database error' })
        stats.expiredSubscriptions = result.count
        
        // Get total revenue
        db.get('SELECT SUM(amount) as total FROM payments WHERE status = "completed"', (err, result) => {
          if (err) return res.status(500).json({ message: 'Database error' })
          stats.totalRevenue = result.total || 0
          
          // Get monthly revenue
          db.get(`SELECT SUM(amount) as total FROM payments 
                  WHERE status = "completed" 
                  AND strftime('%Y-%m', payment_date) = strftime('%Y-%m', 'now')`, (err, result) => {
            if (err) return res.status(500).json({ message: 'Database error' })
            stats.monthlyRevenue = result.total || 0
            
            res.json(stats)
          })
        })
      })
    })
  })
})

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`)
  console.log(`📊 Dashboard: http://localhost:3000`)
  console.log(`🔗 API: http://localhost:${PORT}/api`)
})
