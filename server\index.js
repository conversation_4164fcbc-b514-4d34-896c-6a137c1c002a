import express from 'express'
import cors from 'cors'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import sqlite3 from 'sqlite3'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

const app = express()
const PORT = process.env.PORT || 5000
const JWT_SECRET = 'salam-internet-manager-secret-key-2024'

// Middleware
app.use(cors())
app.use(express.json())

// Database setup
const db = new sqlite3.Database(join(__dirname, 'database.db'))

// Initialize database tables with enhanced structure
db.serialize(() => {
  // Users table
  db.run(`CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    name TEXT NOT NULL,
    email TEXT,
    role TEXT DEFAULT 'admin',
    avatar TEXT,
    last_login DATETIME,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`)

  // Customers table with enhanced fields
  db.run(`CREATE TABLE IF NOT EXISTS customers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    phone TEXT NOT NULL,
    email TEXT,
    address TEXT,
    national_id TEXT UNIQUE,
    status TEXT DEFAULT 'active',
    customer_type TEXT DEFAULT 'individual',
    notes TEXT,
    registration_date DATE DEFAULT (date('now')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`)

  // Subscription plans table
  db.run(`CREATE TABLE IF NOT EXISTS subscription_plans (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    speed TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    duration_days INTEGER DEFAULT 30,
    description TEXT,
    features TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`)

  // Subscriptions table with enhanced tracking
  db.run(`CREATE TABLE IF NOT EXISTS subscriptions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    plan_id INTEGER NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status TEXT DEFAULT 'active',
    auto_renew BOOLEAN DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    final_price DECIMAL(10,2),
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans (id)
  )`)

  // Payments table with enhanced tracking
  db.run(`CREATE TABLE IF NOT EXISTS payments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    subscription_id INTEGER,
    amount DECIMAL(10,2) NOT NULL,
    payment_method TEXT NOT NULL,
    payment_date DATE NOT NULL,
    status TEXT DEFAULT 'completed',
    transaction_id TEXT,
    notes TEXT,
    receipt_number TEXT,
    created_by INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES subscriptions (id),
    FOREIGN KEY (created_by) REFERENCES users (id)
  )`)

  // System settings table
  db.run(`CREATE TABLE IF NOT EXISTS system_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key TEXT UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`)

  // Create default admin user
  const hashedPassword = bcrypt.hashSync('admin123', 10)
  db.run(`INSERT OR IGNORE INTO users (username, password, name, email)
          VALUES (?, ?, ?, ?)`,
          ['admin', hashedPassword, 'المدير العام', '<EMAIL>'])

  // Insert enhanced subscription plans
  db.run(`INSERT OR IGNORE INTO subscription_plans (id, name, speed, price, description)
          VALUES
          (1, 'باقة المبتدئ', '50 Mbps', 100.00, 'باقة اقتصادية للاستخدام المنزلي'),
          (2, 'باقة العائلة', '100 Mbps', 150.00, 'باقة متوسطة مناسبة للعائلات'),
          (3, 'باقة الأعمال', '200 Mbps', 250.00, 'باقة عالية السرعة للاستخدام المكثف'),
          (4, 'باقة المؤسسات', '500 Mbps', 500.00, 'باقة احترافية للشركات والمؤسسات')`)

  // Insert sample customers
  db.run(`INSERT OR IGNORE INTO customers (id, name, phone, email, address, national_id, customer_type)
          VALUES
          (1, 'محمد أحمد السالم', '0501234567', '<EMAIL>', 'الرياض - حي النرجس', '1234567890', 'individual'),
          (2, 'سارة محمود الأحمد', '0507654321', '<EMAIL>', 'جدة - حي الروضة', '0987654321', 'individual'),
          (3, 'أحمد علي المحمد', '0551234567', '<EMAIL>', 'الدمام - حي الفيصلية', '1122334455', 'individual'),
          (4, 'شركة التقنية المتقدمة', '0112345678', '<EMAIL>', 'الرياض - حي العليا', '7001234567', 'business')`)

  // Insert sample subscriptions
  db.run(`INSERT OR IGNORE INTO subscriptions (id, customer_id, plan_id, start_date, end_date, final_price, auto_renew)
          VALUES
          (1, 1, 2, '2024-01-01', '2024-02-01', 150.00, 1),
          (2, 2, 3, '2024-01-15', '2024-02-15', 250.00, 1),
          (3, 3, 1, '2023-12-01', '2024-01-01', 100.00, 0),
          (4, 4, 4, '2024-01-10', '2024-02-10', 500.00, 1)`)

  // Insert sample payments
  db.run(`INSERT OR IGNORE INTO payments (id, customer_id, subscription_id, amount, payment_method, payment_date, receipt_number)
          VALUES
          (1, 1, 1, 150.00, 'نقدي', '2024-01-01', 'REC-001'),
          (2, 2, 2, 250.00, 'تحويل بنكي', '2024-01-15', 'REC-002'),
          (3, 3, 3, 100.00, 'بطاقة ائتمان', '2023-12-01', 'REC-003'),
          (4, 4, 4, 500.00, 'شيك', '2024-01-10', 'REC-004')`)

  // Insert system settings
  db.run(`INSERT OR IGNORE INTO system_settings (setting_key, setting_value, description)
          VALUES
          ('company_name', 'سلام للإنترنت', 'اسم الشركة'),
          ('company_phone', '0112345678', 'رقم هاتف الشركة'),
          ('company_email', '<EMAIL>', 'بريد الشركة الإلكتروني'),
          ('currency', 'ريال سعودي', 'العملة المستخدمة'),
          ('tax_rate', '15', 'نسبة الضريبة المضافة'),
          ('late_fee', '50', 'رسوم التأخير')`)

  console.log('✅ Database initialized with enhanced structure and sample data')
})

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.status(401).json({ message: 'Access token required' })
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid or expired token' })
    }
    req.user = user
    next()
  })
}

// Auth routes
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body

  db.get('SELECT * FROM users WHERE username = ?', [username], (err, user) => {
    if (err) {
      return res.status(500).json({ message: 'Database error' })
    }

    if (!user || !bcrypt.compareSync(password, user.password)) {
      return res.status(401).json({ message: 'Invalid credentials' })
    }

    const token = jwt.sign(
      { id: user.id, username: user.username, name: user.name },
      JWT_SECRET,
      { expiresIn: '24h' }
    )

    res.json({
      token,
      user: {
        id: user.id,
        username: user.username,
        name: user.name,
        email: user.email,
        role: user.role
      }
    })
  })
})

app.get('/api/auth/me', authenticateToken, (req, res) => {
  db.get('SELECT id, username, name, email, role FROM users WHERE id = ?', [req.user.id], (err, user) => {
    if (err) {
      return res.status(500).json({ message: 'Database error' })
    }
    if (!user) {
      return res.status(404).json({ message: 'User not found' })
    }
    res.json({ user })
  })
})

// Enhanced Customers routes with full CRUD operations
app.get('/api/customers', authenticateToken, (req, res) => {
  const { search, status, type, page = 1, limit = 10 } = req.query
  let query = 'SELECT * FROM customers WHERE 1=1'
  let params = []

  if (search) {
    query += ' AND (name LIKE ? OR phone LIKE ? OR email LIKE ?)'
    params.push(`%${search}%`, `%${search}%`, `%${search}%`)
  }

  if (status) {
    query += ' AND status = ?'
    params.push(status)
  }

  if (type) {
    query += ' AND customer_type = ?'
    params.push(type)
  }

  query += ' ORDER BY created_at DESC'

  if (limit !== 'all') {
    const offset = (page - 1) * limit
    query += ' LIMIT ? OFFSET ?'
    params.push(parseInt(limit), offset)
  }

  db.all(query, params, (err, customers) => {
    if (err) {
      console.error('Database error:', err)
      return res.status(500).json({ message: 'Database error', error: err.message })
    }

    // Get total count for pagination
    db.get('SELECT COUNT(*) as total FROM customers', (err, countResult) => {
      if (err) {
        return res.status(500).json({ message: 'Database error' })
      }

      res.json({
        customers,
        pagination: {
          total: countResult.total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(countResult.total / limit)
        }
      })
    })
  })
})

app.post('/api/customers', authenticateToken, (req, res) => {
  const { name, phone, email, address, national_id, customer_type, notes } = req.body

  // Validation
  if (!name || !phone) {
    return res.status(400).json({ message: 'Name and phone are required' })
  }

  db.run(
    `INSERT INTO customers (name, phone, email, address, national_id, customer_type, notes, updated_at)
     VALUES (?, ?, ?, ?, ?, ?, ?, datetime('now'))`,
    [name, phone, email || null, address || null, national_id || null, customer_type || 'individual', notes || null],
    function(err) {
      if (err) {
        console.error('Database error:', err)
        if (err.message.includes('UNIQUE constraint failed')) {
          return res.status(400).json({ message: 'رقم الهوية مستخدم مسبقاً' })
        }
        return res.status(500).json({ message: 'Database error', error: err.message })
      }

      // Get the created customer
      db.get('SELECT * FROM customers WHERE id = ?', [this.lastID], (err, customer) => {
        if (err) {
          return res.status(500).json({ message: 'Database error' })
        }
        res.status(201).json({
          message: 'تم إضافة العميل بنجاح',
          customer
        })
      })
    }
  )
})

app.put('/api/customers/:id', authenticateToken, (req, res) => {
  const { id } = req.params
  const { name, phone, email, address, national_id, customer_type, notes, status } = req.body

  if (!name || !phone) {
    return res.status(400).json({ message: 'Name and phone are required' })
  }

  db.run(
    `UPDATE customers
     SET name = ?, phone = ?, email = ?, address = ?, national_id = ?,
         customer_type = ?, notes = ?, status = ?, updated_at = datetime('now')
     WHERE id = ?`,
    [name, phone, email, address, national_id, customer_type, notes, status, id],
    function(err) {
      if (err) {
        console.error('Database error:', err)
        if (err.message.includes('UNIQUE constraint failed')) {
          return res.status(400).json({ message: 'رقم الهوية مستخدم مسبقاً' })
        }
        return res.status(500).json({ message: 'Database error', error: err.message })
      }

      if (this.changes === 0) {
        return res.status(404).json({ message: 'Customer not found' })
      }

      // Get the updated customer
      db.get('SELECT * FROM customers WHERE id = ?', [id], (err, customer) => {
        if (err) {
          return res.status(500).json({ message: 'Database error' })
        }
        res.json({
          message: 'تم تحديث بيانات العميل بنجاح',
          customer
        })
      })
    }
  )
})

app.delete('/api/customers/:id', authenticateToken, (req, res) => {
  const { id } = req.params

  // Check if customer has active subscriptions
  db.get('SELECT COUNT(*) as count FROM subscriptions WHERE customer_id = ? AND status = "active"', [id], (err, result) => {
    if (err) {
      return res.status(500).json({ message: 'Database error' })
    }

    if (result.count > 0) {
      return res.status(400).json({ message: 'لا يمكن حذف العميل لوجود اشتراكات نشطة' })
    }

    db.run('DELETE FROM customers WHERE id = ?', [id], function(err) {
      if (err) {
        console.error('Database error:', err)
        return res.status(500).json({ message: 'Database error', error: err.message })
      }

      if (this.changes === 0) {
        return res.status(404).json({ message: 'Customer not found' })
      }

      res.json({ message: 'تم حذف العميل بنجاح' })
    })
  })
})

app.get('/api/customers/:id', authenticateToken, (req, res) => {
  const { id } = req.params

  db.get('SELECT * FROM customers WHERE id = ?', [id], (err, customer) => {
    if (err) {
      return res.status(500).json({ message: 'Database error' })
    }

    if (!customer) {
      return res.status(404).json({ message: 'Customer not found' })
    }

    // Get customer's subscriptions and payments
    db.all(`
      SELECT s.*, sp.name as plan_name, sp.speed, sp.price as plan_price
      FROM subscriptions s
      JOIN subscription_plans sp ON s.plan_id = sp.id
      WHERE s.customer_id = ?
      ORDER BY s.created_at DESC
    `, [id], (err, subscriptions) => {
      if (err) {
        return res.status(500).json({ message: 'Database error' })
      }

      db.all(`
        SELECT p.*, s.start_date, s.end_date
        FROM payments p
        LEFT JOIN subscriptions s ON p.subscription_id = s.id
        WHERE p.customer_id = ?
        ORDER BY p.payment_date DESC
      `, [id], (err, payments) => {
        if (err) {
          return res.status(500).json({ message: 'Database error' })
        }

        res.json({
          customer,
          subscriptions,
          payments
        })
      })
    })
  })
})

// Subscriptions routes
app.get('/api/subscriptions', authenticateToken, (req, res) => {
  const query = `
    SELECT s.*, c.name as customer_name, sp.name as plan_name, sp.price
    FROM subscriptions s
    JOIN customers c ON s.customer_id = c.id
    JOIN subscription_plans sp ON s.plan_id = sp.id
    ORDER BY s.created_at DESC
  `
  
  db.all(query, (err, subscriptions) => {
    if (err) {
      return res.status(500).json({ message: 'Database error' })
    }
    res.json(subscriptions)
  })
})

// Payments routes
app.get('/api/payments', authenticateToken, (req, res) => {
  const query = `
    SELECT p.*, c.name as customer_name
    FROM payments p
    JOIN customers c ON p.customer_id = c.id
    ORDER BY p.created_at DESC
  `
  
  db.all(query, (err, payments) => {
    if (err) {
      return res.status(500).json({ message: 'Database error' })
    }
    res.json(payments)
  })
})

// Dashboard stats
app.get('/api/dashboard/stats', authenticateToken, (req, res) => {
  const stats = {}
  
  // Get total customers
  db.get('SELECT COUNT(*) as count FROM customers', (err, result) => {
    if (err) return res.status(500).json({ message: 'Database error' })
    stats.totalCustomers = result.count
    
    // Get active subscriptions
    db.get('SELECT COUNT(*) as count FROM subscriptions WHERE status = "active"', (err, result) => {
      if (err) return res.status(500).json({ message: 'Database error' })
      stats.activeSubscriptions = result.count
      
      // Get expired subscriptions
      db.get('SELECT COUNT(*) as count FROM subscriptions WHERE end_date < date("now")', (err, result) => {
        if (err) return res.status(500).json({ message: 'Database error' })
        stats.expiredSubscriptions = result.count
        
        // Get total revenue
        db.get('SELECT SUM(amount) as total FROM payments WHERE status = "completed"', (err, result) => {
          if (err) return res.status(500).json({ message: 'Database error' })
          stats.totalRevenue = result.total || 0
          
          // Get monthly revenue
          db.get(`SELECT SUM(amount) as total FROM payments 
                  WHERE status = "completed" 
                  AND strftime('%Y-%m', payment_date) = strftime('%Y-%m', 'now')`, (err, result) => {
            if (err) return res.status(500).json({ message: 'Database error' })
            stats.monthlyRevenue = result.total || 0
            
            res.json(stats)
          })
        })
      })
    })
  })
})

// Health check endpoint (no auth required)
app.get('/api/health', (req, res) => {
  res.json({
    status: 'OK',
    message: 'Server is running',
    timestamp: new Date().toISOString(),
    database: 'Connected'
  })
})

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`)
  console.log(`📊 Dashboard: http://localhost:3000`)
  console.log(`🔗 API: http://localhost:${PORT}/api`)
  console.log(`❤️ Health: http://localhost:${PORT}/api/health`)
})
