import React, { useState } from 'react'
import { useAuth } from '../../context/AuthContext'

const Login = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  })
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const { login } = useAuth()

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    })
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsLoading(true)
    
    const result = await login(formData.username, formData.password)
    
    setIsLoading(false)
  }

  return (
    <div className="min-vh-100 d-flex align-items-center justify-content-center p-4">
      <div className="container">
        <div className="row justify-content-center">
          <div className="col-md-6 col-lg-5">
            <div className="card-modern p-5 fade-in">
              {/* Logo and Title */}
              <div className="text-center mb-5">
                <div className="mb-4">
                  <i className="fas fa-wifi text-primary" style={{ fontSize: '4rem' }}></i>
                </div>
                <h2 className="fw-bold text-primary mb-2">سلام</h2>
                <p className="text-muted">نظام إدارة اشتراكات الإنترنت</p>
              </div>

              {/* Login Form */}
              <form onSubmit={handleSubmit}>
                <div className="mb-4">
                  <label htmlFor="username" className="form-label fw-semibold">
                    <i className="fas fa-user me-2"></i>
                    اسم المستخدم
                  </label>
                  <input
                    type="text"
                    className="form-control form-control-modern"
                    id="username"
                    name="username"
                    value={formData.username}
                    onChange={handleChange}
                    placeholder="أدخل اسم المستخدم"
                    required
                  />
                </div>

                <div className="mb-4">
                  <label htmlFor="password" className="form-label fw-semibold">
                    <i className="fas fa-lock me-2"></i>
                    كلمة المرور
                  </label>
                  <div className="position-relative">
                    <input
                      type={showPassword ? 'text' : 'password'}
                      className="form-control form-control-modern"
                      id="password"
                      name="password"
                      value={formData.password}
                      onChange={handleChange}
                      placeholder="أدخل كلمة المرور"
                      required
                    />
                    <button
                      type="button"
                      className="btn btn-link position-absolute top-50 start-0 translate-middle-y"
                      onClick={() => setShowPassword(!showPassword)}
                      style={{ zIndex: 10 }}
                    >
                      <i className={`fas ${showPassword ? 'fa-eye-slash' : 'fa-eye'}`}></i>
                    </button>
                  </div>
                </div>

                <div className="mb-4">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="checkbox"
                      id="rememberMe"
                    />
                    <label className="form-check-label" htmlFor="rememberMe">
                      تذكرني
                    </label>
                  </div>
                </div>

                <button
                  type="submit"
                  className="btn btn-primary-modern w-100 mb-3"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <span className="loading-spinner me-2"></span>
                      جاري تسجيل الدخول...
                    </>
                  ) : (
                    <>
                      <i className="fas fa-sign-in-alt me-2"></i>
                      تسجيل الدخول
                    </>
                  )}
                </button>
              </form>

              {/* Footer */}
              <div className="text-center mt-4">
                <small className="text-muted">
                  © 2024 سلام - نظام إدارة اشتراكات الإنترنت
                </small>
              </div>
            </div>

            {/* Demo Credentials */}
            <div className="card-modern mt-4 p-3">
              <div className="text-center">
                <h6 className="text-primary mb-2">
                  <i className="fas fa-info-circle me-2"></i>
                  بيانات تجريبية
                </h6>
                <small className="text-muted d-block">اسم المستخدم: admin</small>
                <small className="text-muted d-block">كلمة المرور: admin123</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Login
