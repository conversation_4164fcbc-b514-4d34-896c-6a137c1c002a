import React from 'react'

const Settings = () => {
  return (
    <div className="fade-in">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="fw-bold text-dark mb-1">الإعدادات</h2>
          <p className="text-muted mb-0">إعدادات النظام والتطبيق</p>
        </div>
      </div>

      <div className="row">
        <div className="col-md-6 mb-4">
          <div className="card-modern">
            <div className="card-body">
              <h5 className="card-title">
                <i className="fas fa-cog text-primary me-2"></i>
                إعدادات عامة
              </h5>
              <div className="mb-3">
                <label className="form-label">اسم الشركة</label>
                <input type="text" className="form-control form-control-modern" value="سلام للإنترنت" />
              </div>
              <div className="mb-3">
                <label className="form-label">رقم الهاتف</label>
                <input type="text" className="form-control form-control-modern" value="0501234567" />
              </div>
              <button className="btn btn-primary-modern">حفظ التغييرات</button>
            </div>
          </div>
        </div>
        <div className="col-md-6 mb-4">
          <div className="card-modern">
            <div className="card-body">
              <h5 className="card-title">
                <i className="fas fa-bell text-warning me-2"></i>
                إعدادات الإشعارات
              </h5>
              <div className="form-check mb-3">
                <input className="form-check-input" type="checkbox" id="emailNotifications" checked />
                <label className="form-check-label" htmlFor="emailNotifications">
                  إشعارات البريد الإلكتروني
                </label>
              </div>
              <div className="form-check mb-3">
                <input className="form-check-input" type="checkbox" id="smsNotifications" checked />
                <label className="form-check-label" htmlFor="smsNotifications">
                  إشعارات الرسائل النصية
                </label>
              </div>
              <button className="btn btn-warning">حفظ الإعدادات</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Settings
