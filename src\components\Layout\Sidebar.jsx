import React from 'react'
import { Link, useLocation } from 'react-router-dom'

const Sidebar = ({ isOpen, onClose }) => {
  const location = useLocation()

  const menuItems = [
    {
      path: '/dashboard',
      icon: 'fas fa-tachometer-alt',
      label: 'لوحة التحكم'
    },
    {
      path: '/customers',
      icon: 'fas fa-users',
      label: 'العملاء'
    },
    {
      path: '/subscriptions',
      icon: 'fas fa-wifi',
      label: 'الاشتراكات'
    },
    {
      path: '/payments',
      icon: 'fas fa-money-bill-wave',
      label: 'المدفوعات'
    },
    {
      path: '/reports',
      icon: 'fas fa-chart-bar',
      label: 'التقارير'
    },
    {
      path: '/settings',
      icon: 'fas fa-cog',
      label: 'الإعدادات'
    }
  ]

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="position-fixed top-0 start-0 w-100 h-100 bg-dark bg-opacity-50 d-lg-none"
          style={{ zIndex: 999 }}
          onClick={onClose}
        ></div>
      )}

      {/* Sidebar */}
      <div className={`sidebar-modern ${isOpen ? 'show' : ''} d-lg-block`} style={{ width: '250px', zIndex: 1000 }}>
        {/* Header */}
        <div className="p-4 border-bottom border-light border-opacity-25">
          <div className="d-flex align-items-center justify-content-between">
            <div className="d-flex align-items-center">
              <i className="fas fa-wifi me-2" style={{ fontSize: '1.5rem' }}></i>
              <h5 className="mb-0 fw-bold">سلام</h5>
            </div>
            <button
              className="btn btn-link text-white d-lg-none p-0"
              onClick={onClose}
            >
              <i className="fas fa-times"></i>
            </button>
          </div>
          <small className="text-light opacity-75">نظام إدارة الاشتراكات</small>
        </div>

        {/* Menu Items */}
        <div className="p-2">
          {menuItems.map((item) => (
            <Link
              key={item.path}
              to={item.path}
              className={`sidebar-item d-flex align-items-center text-decoration-none text-white ${
                location.pathname === item.path ? 'active' : ''
              }`}
              onClick={onClose}
            >
              <i className={`${item.icon} me-3`} style={{ width: '20px' }}></i>
              <span>{item.label}</span>
            </Link>
          ))}
        </div>

        {/* Footer */}
        <div className="mt-auto p-4 border-top border-light border-opacity-25">
          <div className="text-center">
            <small className="text-light opacity-75">
              الإصدار 1.0.0
            </small>
          </div>
        </div>
      </div>
    </>
  )
}

export default Sidebar
