// Serverless API for Vercel
import express from 'express'
import cors from 'cors'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

const app = express()
const JWT_SECRET = 'salam-internet-manager-secret-key-2024'

// Middleware
app.use(cors())
app.use(express.json())

// In-memory database for demo (في الإنتاج استخدم قاعدة بيانات حقيقية)
let users = [
  {
    id: 1,
    username: 'admin',
    password: bcrypt.hashSync('admin123', 10),
    name: 'المدير العام',
    email: '<EMAIL>',
    role: 'admin'
  }
]

let customers = [
  { id: 1, name: 'محمد أحمد', phone: '0501234567', email: '<EMAIL>', address: 'الرياض', national_id: '1234567890', status: 'active' },
  { id: 2, name: 'سارة محمود', phone: '0507654321', email: '<EMAIL>', address: 'جدة', national_id: '0987654321', status: 'active' },
  { id: 3, name: 'أحمد علي', phone: '0551234567', email: '<EMAIL>', address: 'الدمام', national_id: '1122334455', status: 'pending' }
]

let subscriptions = [
  { id: 1, customer_id: 1, plan_name: 'باقة 100 ميجا', price: 150, start_date: '2024-01-01', end_date: '2024-02-01', status: 'active' },
  { id: 2, customer_id: 2, plan_name: 'باقة 200 ميجا', price: 250, start_date: '2024-01-15', end_date: '2024-02-15', status: 'active' },
  { id: 3, customer_id: 3, plan_name: 'باقة 50 ميجا', price: 100, start_date: '2023-12-01', end_date: '2024-01-01', status: 'expired' }
]

let payments = [
  { id: 1, customer_id: 1, amount: 150, payment_date: '2024-01-15', payment_method: 'نقدي', status: 'completed' },
  { id: 2, customer_id: 2, amount: 250, payment_date: '2024-01-14', payment_method: 'تحويل بنكي', status: 'completed' },
  { id: 3, customer_id: 3, amount: 100, payment_date: '2024-01-13', payment_method: 'نقدي', status: 'pending' }
]

// Authentication middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.status(401).json({ message: 'Access token required' })
  }

  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ message: 'Invalid or expired token' })
    }
    req.user = user
    next()
  })
}

// Auth routes
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body

  const user = users.find(u => u.username === username)
  
  if (!user || !bcrypt.compareSync(password, user.password)) {
    return res.status(401).json({ message: 'Invalid credentials' })
  }

  const token = jwt.sign(
    { id: user.id, username: user.username, name: user.name },
    JWT_SECRET,
    { expiresIn: '24h' }
  )

  res.json({
    token,
    user: {
      id: user.id,
      username: user.username,
      name: user.name,
      email: user.email,
      role: user.role
    }
  })
})

app.get('/api/auth/me', authenticateToken, (req, res) => {
  const user = users.find(u => u.id === req.user.id)
  if (!user) {
    return res.status(404).json({ message: 'User not found' })
  }
  res.json({ user: { id: user.id, username: user.username, name: user.name, email: user.email, role: user.role } })
})

// Customers routes
app.get('/api/customers', authenticateToken, (req, res) => {
  res.json(customers)
})

app.post('/api/customers', authenticateToken, (req, res) => {
  const { name, phone, email, address, national_id } = req.body
  const newCustomer = {
    id: customers.length + 1,
    name,
    phone,
    email,
    address,
    national_id,
    status: 'active'
  }
  customers.push(newCustomer)
  res.json({ id: newCustomer.id, message: 'Customer created successfully' })
})

// Subscriptions routes
app.get('/api/subscriptions', authenticateToken, (req, res) => {
  const subscriptionsWithCustomers = subscriptions.map(sub => {
    const customer = customers.find(c => c.id === sub.customer_id)
    return {
      ...sub,
      customer_name: customer ? customer.name : 'غير معروف'
    }
  })
  res.json(subscriptionsWithCustomers)
})

// Payments routes
app.get('/api/payments', authenticateToken, (req, res) => {
  const paymentsWithCustomers = payments.map(payment => {
    const customer = customers.find(c => c.id === payment.customer_id)
    return {
      ...payment,
      customer_name: customer ? customer.name : 'غير معروف'
    }
  })
  res.json(paymentsWithCustomers)
})

// Dashboard stats
app.get('/api/dashboard/stats', authenticateToken, (req, res) => {
  const stats = {
    totalCustomers: customers.length,
    activeSubscriptions: subscriptions.filter(s => s.status === 'active').length,
    expiredSubscriptions: subscriptions.filter(s => s.status === 'expired').length,
    totalRevenue: payments.filter(p => p.status === 'completed').reduce((sum, p) => sum + p.amount, 0),
    monthlyRevenue: payments.filter(p => p.status === 'completed' && p.payment_date.startsWith('2024-01')).reduce((sum, p) => sum + p.amount, 0),
    pendingPayments: payments.filter(p => p.status === 'pending').length
  }
  res.json(stats)
})

// Health check
app.get('/api/health', (req, res) => {
  res.json({ status: 'OK', message: 'Salam API is running', timestamp: new Date().toISOString() })
})

// Root route
app.get('/api', (req, res) => {
  res.json({ 
    message: 'مرحباً بك في API تطبيق سلام لإدارة اشتراكات الإنترنت',
    version: '1.0.0',
    endpoints: [
      'POST /api/auth/login',
      'GET /api/auth/me',
      'GET /api/customers',
      'POST /api/customers',
      'GET /api/subscriptions',
      'GET /api/payments',
      'GET /api/dashboard/stats'
    ]
  })
})

export default app
