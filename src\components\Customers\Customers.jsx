import React, { useState, useEffect } from 'react'
import { toast } from 'react-toastify'

const Customers = () => {
  const [customers, setCustomers] = useState([])
  const [loading, setLoading] = useState(true)
  const [showModal, setShowModal] = useState(false)
  const [editingCustomer, setEditingCustomer] = useState(null)
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    nationalId: ''
  })

  useEffect(() => {
    fetchCustomers()
  }, [])

  const fetchCustomers = async () => {
    try {
      // Simulate API call - replace with actual API
      const mockCustomers = [
        { id: 1, name: 'محمد أحمد', phone: '0501234567', email: '<EMAIL>', address: 'الرياض', nationalId: '1234567890', status: 'نشط' },
        { id: 2, name: 'سارة محمود', phone: '0507654321', email: '<EMAIL>', address: 'جدة', nationalId: '0987654321', status: 'نشط' },
        { id: 3, name: 'أحمد علي', phone: '0551234567', email: '<EMAIL>', address: 'الدمام', nationalId: '1122334455', status: 'معلق' }
      ]
      setCustomers(mockCustomers)
    } catch (error) {
      toast.error('خطأ في تحميل بيانات العملاء')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    try {
      if (editingCustomer) {
        // Update customer
        const updatedCustomers = customers.map(customer =>
          customer.id === editingCustomer.id
            ? { ...customer, ...formData }
            : customer
        )
        setCustomers(updatedCustomers)
        toast.success('تم تحديث بيانات العميل بنجاح')
      } else {
        // Add new customer
        const newCustomer = {
          id: Date.now(),
          ...formData,
          status: 'نشط'
        }
        setCustomers([...customers, newCustomer])
        toast.success('تم إضافة العميل بنجاح')
      }
      
      setShowModal(false)
      setEditingCustomer(null)
      setFormData({ name: '', phone: '', email: '', address: '', nationalId: '' })
    } catch (error) {
      toast.error('خطأ في حفظ بيانات العميل')
    }
  }

  const handleEdit = (customer) => {
    setEditingCustomer(customer)
    setFormData({
      name: customer.name,
      phone: customer.phone,
      email: customer.email,
      address: customer.address,
      nationalId: customer.nationalId
    })
    setShowModal(true)
  }

  const handleDelete = (customerId) => {
    if (window.confirm('هل أنت متأكد من حذف هذا العميل؟')) {
      setCustomers(customers.filter(customer => customer.id !== customerId))
      toast.success('تم حذف العميل بنجاح')
    }
  }

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="text-center">
          <div className="loading-spinner mb-3"></div>
          <h5>جاري تحميل بيانات العملاء...</h5>
        </div>
      </div>
    )
  }

  return (
    <div className="fade-in">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="fw-bold text-dark mb-1">إدارة العملاء</h2>
          <p className="text-muted mb-0">إدارة بيانات العملاء والمشتركين</p>
        </div>
        <button
          className="btn btn-primary-modern"
          onClick={() => setShowModal(true)}
        >
          <i className="fas fa-plus me-2"></i>
          إضافة عميل جديد
        </button>
      </div>

      {/* Customers Table */}
      <div className="card-modern">
        <div className="card-body p-0">
          <div className="table-responsive">
            <table className="table table-modern mb-0">
              <thead>
                <tr>
                  <th>الاسم</th>
                  <th>رقم الهاتف</th>
                  <th>البريد الإلكتروني</th>
                  <th>العنوان</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {customers.map((customer) => (
                  <tr key={customer.id}>
                    <td>
                      <div className="d-flex align-items-center">
                        <div className="rounded-circle bg-primary bg-opacity-10 p-2 me-3">
                          <i className="fas fa-user text-primary"></i>
                        </div>
                        <div>
                          <div className="fw-semibold">{customer.name}</div>
                          <small className="text-muted">{customer.nationalId}</small>
                        </div>
                      </div>
                    </td>
                    <td>{customer.phone}</td>
                    <td>{customer.email}</td>
                    <td>{customer.address}</td>
                    <td>
                      <span className={`status-badge ${customer.status === 'نشط' ? 'status-active' : 'status-pending'}`}>
                        {customer.status}
                      </span>
                    </td>
                    <td>
                      <div className="btn-group">
                        <button
                          className="btn btn-sm btn-outline-primary"
                          onClick={() => handleEdit(customer)}
                        >
                          <i className="fas fa-edit"></i>
                        </button>
                        <button
                          className="btn btn-sm btn-outline-danger"
                          onClick={() => handleDelete(customer.id)}
                        >
                          <i className="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>

      {/* Add/Edit Customer Modal */}
      {showModal && (
        <div className="modal show d-block" style={{ backgroundColor: 'rgba(0,0,0,0.5)' }}>
          <div className="modal-dialog modal-lg">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">
                  {editingCustomer ? 'تعديل بيانات العميل' : 'إضافة عميل جديد'}
                </h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => {
                    setShowModal(false)
                    setEditingCustomer(null)
                    setFormData({ name: '', phone: '', email: '', address: '', nationalId: '' })
                  }}
                ></button>
              </div>
              <form onSubmit={handleSubmit}>
                <div className="modal-body">
                  <div className="row">
                    <div className="col-md-6 mb-3">
                      <label className="form-label">الاسم الكامل</label>
                      <input
                        type="text"
                        className="form-control form-control-modern"
                        value={formData.name}
                        onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                        required
                      />
                    </div>
                    <div className="col-md-6 mb-3">
                      <label className="form-label">رقم الهاتف</label>
                      <input
                        type="tel"
                        className="form-control form-control-modern"
                        value={formData.phone}
                        onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                        required
                      />
                    </div>
                    <div className="col-md-6 mb-3">
                      <label className="form-label">البريد الإلكتروني</label>
                      <input
                        type="email"
                        className="form-control form-control-modern"
                        value={formData.email}
                        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                      />
                    </div>
                    <div className="col-md-6 mb-3">
                      <label className="form-label">رقم الهوية</label>
                      <input
                        type="text"
                        className="form-control form-control-modern"
                        value={formData.nationalId}
                        onChange={(e) => setFormData({ ...formData, nationalId: e.target.value })}
                        required
                      />
                    </div>
                    <div className="col-12 mb-3">
                      <label className="form-label">العنوان</label>
                      <textarea
                        className="form-control form-control-modern"
                        rows="3"
                        value={formData.address}
                        onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                        required
                      ></textarea>
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={() => {
                      setShowModal(false)
                      setEditingCustomer(null)
                      setFormData({ name: '', phone: '', email: '', address: '', nationalId: '' })
                    }}
                  >
                    إلغاء
                  </button>
                  <button type="submit" className="btn btn-primary-modern">
                    {editingCustomer ? 'تحديث' : 'إضافة'}
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default Customers
