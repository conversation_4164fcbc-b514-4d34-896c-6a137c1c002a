<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/primary_color">

    <!-- شاشة التحميل -->
    <LinearLayout
        android:id="@+id/splash_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:background="@color/primary_color"
        android:visibility="gone">
        
        <ImageView
            android:layout_width="120dp"
            android:layout_height="120dp"
            android:src="@drawable/ic_wifi"
            android:layout_marginBottom="20dp"
            android:tint="@android:color/white" />
        
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="سلام"
            android:textSize="32sp"
            android:textColor="@android:color/white"
            android:textStyle="bold"
            android:layout_marginBottom="10dp" />
        
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="نظام إدارة اشتراكات الإنترنت"
            android:textSize="16sp"
            android:textColor="@android:color/white"
            android:alpha="0.8" />
        
        <ProgressBar
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:indeterminateTint="@android:color/white" />
    </LinearLayout>

    <!-- شريط التقدم -->
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleHorizontal"
        android:layout_width="match_parent"
        android:layout_height="4dp"
        android:layout_alignParentTop="true"
        android:progressTint="@android:color/white"
        android:progressBackgroundTint="@color/primary_color_dark"
        android:visibility="gone" />

    <!-- WebView -->
    <WebView
        android:id="@+id/webview"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/progressBar" />

</RelativeLayout>
