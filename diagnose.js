import axios from 'axios'

console.log('🔍 تشخيص مشاكل التطبيق...')
console.log('================================')

// تحقق من الخادم
async function checkServer() {
  try {
    console.log('🔗 فحص الخادم على http://localhost:5000...')
    const response = await axios.get('http://localhost:5000/api/health')
    console.log('✅ الخادم يعمل بشكل صحيح')
    console.log('📊 الحالة:', response.data.status)
    console.log('⏰ الوقت:', response.data.timestamp)
    return true
  } catch (error) {
    console.log('❌ الخادم لا يعمل:', error.message)
    if (error.code === 'ECONNREFUSED') {
      console.log('💡 الحل: شغل الخادم بالأمر: npm run server')
    }
    return false
  }
}

// تحقق من تسجيل الدخول
async function checkLogin() {
  try {
    console.log('🔐 فحص تسجيل الدخول...')
    const response = await axios.post('http://localhost:5000/api/auth/login', {
      username: 'admin',
      password: 'admin123'
    })
    console.log('✅ تسجيل الدخول يعمل')
    console.log('🎫 Token:', response.data.token ? 'موجود' : 'غير موجود')
    return true
  } catch (error) {
    console.log('❌ مشكلة في تسجيل الدخول:', error.message)
    return false
  }
}

// تحقق من قاعدة البيانات
async function checkDatabase() {
  try {
    console.log('🗄️ فحص قاعدة البيانات...')
    const response = await axios.get('http://localhost:5000/api/customers')
    console.log('✅ قاعدة البيانات تعمل')
    console.log('👥 عدد العملاء:', response.data.customers?.length || response.data.length || 0)
    return true
  } catch (error) {
    console.log('❌ مشكلة في قاعدة البيانات:', error.message)
    return false
  }
}

// تشغيل التشخيص
async function runDiagnosis() {
  console.log('🚀 بدء التشخيص...')
  console.log('')
  
  const serverOk = await checkServer()
  console.log('')
  
  if (serverOk) {
    const loginOk = await checkLogin()
    console.log('')
    
    if (loginOk) {
      const dbOk = await checkDatabase()
      console.log('')
      
      if (dbOk) {
        console.log('🎉 جميع الأنظمة تعمل بشكل صحيح!')
        console.log('📱 يمكنك فتح التطبيق على: http://localhost:3000')
        console.log('🔐 بيانات الدخول: admin / admin123')
      }
    }
  } else {
    console.log('🔧 يجب تشغيل الخادم أولاً:')
    console.log('   npm run server')
  }
  
  console.log('')
  console.log('================================')
  console.log('انتهى التشخيص')
}

runDiagnosis().catch(console.error)
