import React from 'react'
import { useAuth } from '../../context/AuthContext'

const Navbar = ({ onToggleSidebar }) => {
  const { user, logout } = useAuth()

  return (
    <nav className="navbar navbar-expand-lg navbar-modern sticky-top">
      <div className="container-fluid">
        {/* Mobile Menu Toggle */}
        <button
          className="btn btn-outline-primary d-lg-none me-3"
          onClick={onToggleSidebar}
        >
          <i className="fas fa-bars"></i>
        </button>

        {/* Brand */}
        <div className="navbar-brand d-flex align-items-center">
          <i className="fas fa-wifi text-primary me-2"></i>
          <span className="fw-bold text-primary">سلام</span>
        </div>

        {/* Right Side */}
        <div className="d-flex align-items-center">
          {/* Notifications */}
          <div className="dropdown me-3">
            <button
              className="btn btn-outline-primary position-relative"
              type="button"
              data-bs-toggle="dropdown"
            >
              <i className="fas fa-bell"></i>
              <span className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger">
                3
              </span>
            </button>
            <ul className="dropdown-menu dropdown-menu-end">
              <li><h6 className="dropdown-header">الإشعارات</h6></li>
              <li><a className="dropdown-item" href="#">
                <i className="fas fa-exclamation-triangle text-warning me-2"></i>
                اشتراك محمد أحمد ينتهي غداً
              </a></li>
              <li><a className="dropdown-item" href="#">
                <i className="fas fa-money-bill text-success me-2"></i>
                تم استلام دفعة جديدة
              </a></li>
              <li><a className="dropdown-item" href="#">
                <i className="fas fa-user-plus text-info me-2"></i>
                عميل جديد تم تسجيله
              </a></li>
              <li><hr className="dropdown-divider" /></li>
              <li><a className="dropdown-item text-center" href="#">عرض جميع الإشعارات</a></li>
            </ul>
          </div>

          {/* User Menu */}
          <div className="dropdown">
            <button
              className="btn btn-outline-primary dropdown-toggle d-flex align-items-center"
              type="button"
              data-bs-toggle="dropdown"
            >
              <i className="fas fa-user-circle me-2"></i>
              <span className="d-none d-md-inline">{user?.name || 'المدير'}</span>
            </button>
            <ul className="dropdown-menu dropdown-menu-end">
              <li><h6 className="dropdown-header">مرحباً، {user?.name || 'المدير'}</h6></li>
              <li><a className="dropdown-item" href="#">
                <i className="fas fa-user me-2"></i>
                الملف الشخصي
              </a></li>
              <li><a className="dropdown-item" href="#">
                <i className="fas fa-cog me-2"></i>
                الإعدادات
              </a></li>
              <li><hr className="dropdown-divider" /></li>
              <li>
                <button className="dropdown-item text-danger" onClick={logout}>
                  <i className="fas fa-sign-out-alt me-2"></i>
                  تسجيل الخروج
                </button>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </nav>
  )
}

export default Navbar
