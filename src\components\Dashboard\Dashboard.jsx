import React, { useState, useEffect } from 'react'
import axios from 'axios'
import { toast } from 'react-toastify'
import StatCard from './StatCard'
import RecentActivities from './RecentActivities'
import QuickActions from './QuickActions'

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalCustomers: 0,
    activeSubscriptions: 0,
    expiredSubscriptions: 0,
    totalRevenue: 0,
    monthlyRevenue: 0,
    pendingPayments: 0,
    newCustomersThisMonth: 0,
    averageRevenue: 0
  })
  const [recentActivities, setRecentActivities] = useState([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      setLoading(true)

      // جلب الإحصائيات من API
      const statsResponse = await axios.get('/dashboard/stats')
      setStats(statsResponse.data)

      // جلب النشاطات الأخيرة (محاكاة - يمكن إضافة endpoint حقيقي)
      setRecentActivities([
        {
          id: 1,
          type: 'payment',
          message: 'تم استلام دفعة من محمد أحمد السالم',
          amount: 150,
          created_at: new Date(Date.now() - 10 * 60 * 1000).toISOString(),
          details: 'دفعة اشتراك شهري - باقة العائلة'
        },
        {
          id: 2,
          type: 'subscription',
          message: 'اشتراك جديد لسارة محمود الأحمد',
          created_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          details: 'باقة الأعمال - 200 ميجا'
        },
        {
          id: 3,
          type: 'expiry',
          message: 'اشتراك أحمد علي المحمد ينتهي غداً',
          created_at: new Date(Date.now() - 60 * 60 * 1000).toISOString(),
          details: 'باقة المبتدئ - يحتاج تجديد'
        },
        {
          id: 4,
          type: 'customer',
          message: 'عميل جديد: شركة التقنية المتقدمة',
          created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          details: 'عميل مؤسسي - تم التسجيل بنجاح'
        },
        {
          id: 5,
          type: 'renewal',
          message: 'تم تجديد اشتراك محمد أحمد السالم',
          amount: 150,
          created_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
          details: 'تجديد تلقائي - باقة العائلة'
        }
      ])

      toast.success('تم تحديث البيانات بنجاح')
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
      toast.error('خطأ في تحميل بيانات لوحة التحكم')
    } finally {
      setLoading(false)
    }
  }

  const handleRefresh = async () => {
    setRefreshing(true)
    await fetchDashboardData()
    setRefreshing(false)
  }

  // تم نقل StatCard إلى مكون منفصل

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="text-center">
          <div className="loading-spinner mb-3"></div>
          <h5>جاري تحميل البيانات...</h5>
        </div>
      </div>
    )
  }

  return (
    <div className="fade-in">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="fw-bold text-white mb-1">
            <i className="fas fa-tachometer-alt me-2"></i>
            لوحة التحكم
          </h2>
          <p className="text-white-50 mb-0">نظرة عامة شاملة على نشاط النظام والإحصائيات</p>
        </div>
        <div>
          <button
            className="btn btn-light btn-lg shadow-sm"
            onClick={handleRefresh}
            disabled={refreshing}
          >
            <i className={`fas fa-sync-alt me-2 ${refreshing ? 'fa-spin' : ''}`}></i>
            {refreshing ? 'جاري التحديث...' : 'تحديث البيانات'}
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="row">
        <StatCard
          title="إجمالي العملاء"
          value={stats.totalCustomers}
          icon="fa-users"
          color="primary"
          trend="up"
          trendValue="12"
        />
        <StatCard
          title="الاشتراكات النشطة"
          value={stats.activeSubscriptions}
          icon="fa-wifi"
          color="secondary"
          trend="up"
          trendValue="8"
        />
        <StatCard
          title="الاشتراكات المنتهية"
          value={stats.expiredSubscriptions}
          icon="fa-exclamation-triangle"
          color="warning"
          trend="down"
          trendValue="5"
        />
        <StatCard
          title="الإيرادات الشهرية"
          value={stats.monthlyRevenue}
          icon="fa-money-bill-wave"
          color="success"
          subtitle="ريال سعودي"
          trend="up"
          trendValue="15"
        />
      </div>

      {/* الصف الثاني من الإحصائيات */}
      <div className="row">
        <StatCard
          title="إجمالي الإيرادات"
          value={stats.totalRevenue}
          icon="fa-chart-line"
          color="info"
          subtitle="ريال سعودي"
          trend="up"
          trendValue="25"
        />
        <StatCard
          title="المدفوعات المعلقة"
          value={stats.pendingPayments}
          icon="fa-clock"
          color="danger"
          trend="down"
          trendValue="3"
        />
        <StatCard
          title="عملاء جدد هذا الشهر"
          value={stats.newCustomersThisMonth || 12}
          icon="fa-user-plus"
          color="primary"
          trend="up"
          trendValue="20"
        />
        <StatCard
          title="متوسط الإيرادات"
          value={Math.round(stats.totalRevenue / (stats.totalCustomers || 1))}
          icon="fa-calculator"
          color="secondary"
          subtitle="ريال لكل عميل"
        />
      </div>

      {/* Recent Activities and Quick Actions */}
      <div className="row">
        {/* Recent Activities */}
        <div className="col-lg-8 mb-4">
          <RecentActivities activities={recentActivities} loading={loading} />
        </div>

        {/* Quick Actions */}
        <div className="col-lg-4 mb-4">
          <QuickActions />
        </div>
      </div>
    </div>
  )
}

export default Dashboard
