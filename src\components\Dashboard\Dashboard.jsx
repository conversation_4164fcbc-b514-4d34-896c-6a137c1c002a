import React, { useState, useEffect } from 'react'
import axios from 'axios'

const Dashboard = () => {
  const [stats, setStats] = useState({
    totalCustomers: 0,
    activeSubscriptions: 0,
    expiredSubscriptions: 0,
    totalRevenue: 0,
    monthlyRevenue: 0,
    pendingPayments: 0
  })
  const [recentActivities, setRecentActivities] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchDashboardData()
  }, [])

  const fetchDashboardData = async () => {
    try {
      // Simulate API calls - replace with actual API endpoints
      setStats({
        totalCustomers: 156,
        activeSubscriptions: 142,
        expiredSubscriptions: 14,
        totalRevenue: 45000,
        monthlyRevenue: 12500,
        pendingPayments: 8
      })
      
      setRecentActivities([
        { id: 1, type: 'payment', message: 'تم استلام دفعة من محمد أحمد', time: '10 دقائق', icon: 'fa-money-bill', color: 'success' },
        { id: 2, type: 'subscription', message: 'اشتراك جديد لسارة محمود', time: '30 دقيقة', icon: 'fa-wifi', color: 'info' },
        { id: 3, type: 'expiry', message: 'اشتراك أحمد علي ينتهي غداً', time: '1 ساعة', icon: 'fa-exclamation-triangle', color: 'warning' },
        { id: 4, type: 'customer', message: 'عميل جديد: فاطمة حسن', time: '2 ساعة', icon: 'fa-user-plus', color: 'primary' }
      ])
    } catch (error) {
      console.error('Error fetching dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const StatCard = ({ title, value, icon, color, subtitle }) => (
    <div className="col-md-6 col-lg-4 mb-4">
      <div className="card-modern h-100">
        <div className="card-body">
          <div className="d-flex align-items-center">
            <div className={`rounded-circle p-3 me-3 bg-${color} bg-opacity-10`}>
              <i className={`fas ${icon} text-${color}`} style={{ fontSize: '1.5rem' }}></i>
            </div>
            <div className="flex-grow-1">
              <h6 className="text-muted mb-1">{title}</h6>
              <h3 className="mb-0 fw-bold">{value.toLocaleString()}</h3>
              {subtitle && <small className="text-muted">{subtitle}</small>}
            </div>
          </div>
        </div>
      </div>
    </div>
  )

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="text-center">
          <div className="loading-spinner mb-3"></div>
          <h5>جاري تحميل البيانات...</h5>
        </div>
      </div>
    )
  }

  return (
    <div className="fade-in">
      {/* Page Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="fw-bold text-dark mb-1">لوحة التحكم</h2>
          <p className="text-muted mb-0">نظرة عامة على نشاط النظام</p>
        </div>
        <div>
          <button className="btn btn-primary-modern">
            <i className="fas fa-sync-alt me-2"></i>
            تحديث البيانات
          </button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="row">
        <StatCard
          title="إجمالي العملاء"
          value={stats.totalCustomers}
          icon="fa-users"
          color="primary"
        />
        <StatCard
          title="الاشتراكات النشطة"
          value={stats.activeSubscriptions}
          icon="fa-wifi"
          color="success"
        />
        <StatCard
          title="الاشتراكات المنتهية"
          value={stats.expiredSubscriptions}
          icon="fa-exclamation-triangle"
          color="warning"
        />
        <StatCard
          title="الإيرادات الشهرية"
          value={stats.monthlyRevenue}
          icon="fa-money-bill-wave"
          color="info"
          subtitle="ريال سعودي"
        />
        <StatCard
          title="إجمالي الإيرادات"
          value={stats.totalRevenue}
          icon="fa-chart-line"
          color="success"
          subtitle="ريال سعودي"
        />
        <StatCard
          title="المدفوعات المعلقة"
          value={stats.pendingPayments}
          icon="fa-clock"
          color="danger"
        />
      </div>

      {/* Recent Activities and Quick Actions */}
      <div className="row">
        {/* Recent Activities */}
        <div className="col-lg-8 mb-4">
          <div className="card-modern h-100">
            <div className="card-header bg-transparent border-0 pb-0">
              <h5 className="fw-bold mb-0">
                <i className="fas fa-history me-2 text-primary"></i>
                النشاطات الأخيرة
              </h5>
            </div>
            <div className="card-body">
              <div className="list-group list-group-flush">
                {recentActivities.map((activity) => (
                  <div key={activity.id} className="list-group-item border-0 px-0">
                    <div className="d-flex align-items-center">
                      <div className={`rounded-circle p-2 me-3 bg-${activity.color} bg-opacity-10`}>
                        <i className={`fas ${activity.icon} text-${activity.color}`}></i>
                      </div>
                      <div className="flex-grow-1">
                        <p className="mb-1">{activity.message}</p>
                        <small className="text-muted">منذ {activity.time}</small>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="col-lg-4 mb-4">
          <div className="card-modern h-100">
            <div className="card-header bg-transparent border-0 pb-0">
              <h5 className="fw-bold mb-0">
                <i className="fas fa-bolt me-2 text-primary"></i>
                إجراءات سريعة
              </h5>
            </div>
            <div className="card-body">
              <div className="d-grid gap-3">
                <button className="btn btn-outline-primary text-start">
                  <i className="fas fa-user-plus me-2"></i>
                  إضافة عميل جديد
                </button>
                <button className="btn btn-outline-success text-start">
                  <i className="fas fa-wifi me-2"></i>
                  إنشاء اشتراك جديد
                </button>
                <button className="btn btn-outline-info text-start">
                  <i className="fas fa-money-bill me-2"></i>
                  تسجيل دفعة
                </button>
                <button className="btn btn-outline-warning text-start">
                  <i className="fas fa-chart-bar me-2"></i>
                  عرض التقارير
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Dashboard
