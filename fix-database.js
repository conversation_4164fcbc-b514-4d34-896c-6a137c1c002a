import sqlite3 from 'sqlite3'
import bcrypt from 'bcryptjs'
import { fileURLToPath } from 'url'
import { dirname, join } from 'path'
import fs from 'fs'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// حذف قاعدة البيانات القديمة إذا كانت موجودة
const dbPath = join(__dirname, 'server', 'database.db')
if (fs.existsSync(dbPath)) {
  fs.unlinkSync(dbPath)
  console.log('🗑️ تم حذف قاعدة البيانات القديمة')
}

// إنشاء قاعدة بيانات جديدة
const db = new sqlite3.Database(dbPath)

console.log('🔧 إنشاء قاعدة بيانات جديدة...')

db.serialize(() => {
  // Users table
  db.run(`CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    password TEXT NOT NULL,
    name TEXT NOT NULL,
    email TEXT,
    role TEXT DEFAULT 'admin',
    avatar TEXT,
    last_login DATETIME,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`)

  // Customers table
  db.run(`CREATE TABLE IF NOT EXISTS customers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    phone TEXT NOT NULL,
    email TEXT,
    address TEXT,
    national_id TEXT UNIQUE,
    status TEXT DEFAULT 'active',
    customer_type TEXT DEFAULT 'individual',
    notes TEXT,
    registration_date DATE DEFAULT (date('now')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`)

  // Subscription plans table
  db.run(`CREATE TABLE IF NOT EXISTS subscription_plans (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    speed TEXT NOT NULL,
    price DECIMAL(10,2) NOT NULL,
    duration_days INTEGER DEFAULT 30,
    description TEXT,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`)

  // Subscriptions table
  db.run(`CREATE TABLE IF NOT EXISTS subscriptions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    plan_id INTEGER NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    status TEXT DEFAULT 'active',
    auto_renew BOOLEAN DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    final_price DECIMAL(10,2),
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE,
    FOREIGN KEY (plan_id) REFERENCES subscription_plans (id)
  )`)

  // Payments table
  db.run(`CREATE TABLE IF NOT EXISTS payments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    customer_id INTEGER NOT NULL,
    subscription_id INTEGER,
    amount DECIMAL(10,2) NOT NULL,
    payment_method TEXT NOT NULL,
    payment_date DATE NOT NULL,
    status TEXT DEFAULT 'completed',
    transaction_id TEXT,
    notes TEXT,
    receipt_number TEXT,
    created_by INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers (id) ON DELETE CASCADE,
    FOREIGN KEY (subscription_id) REFERENCES subscriptions (id),
    FOREIGN KEY (created_by) REFERENCES users (id)
  )`)

  // System settings table
  db.run(`CREATE TABLE IF NOT EXISTS system_settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    setting_key TEXT UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
  )`)

  // إدراج البيانات الأساسية
  const hashedPassword = bcrypt.hashSync('admin123', 10)
  db.run(`INSERT INTO users (username, password, name, email) 
          VALUES (?, ?, ?, ?)`, 
          ['admin', hashedPassword, 'المدير العام', '<EMAIL>'])

  // إدراج الباقات
  db.run(`INSERT INTO subscription_plans (id, name, speed, price, description) 
          VALUES 
          (1, 'باقة المبتدئ', '50 Mbps', 100.00, 'باقة اقتصادية للاستخدام المنزلي'),
          (2, 'باقة العائلة', '100 Mbps', 150.00, 'باقة متوسطة مناسبة للعائلات'),
          (3, 'باقة الأعمال', '200 Mbps', 250.00, 'باقة عالية السرعة للاستخدام المكثف'),
          (4, 'باقة المؤسسات', '500 Mbps', 500.00, 'باقة احترافية للشركات والمؤسسات')`)

  // إدراج العملاء التجريبيين
  db.run(`INSERT INTO customers (id, name, phone, email, address, national_id, customer_type) 
          VALUES 
          (1, 'محمد أحمد السالم', '0501234567', '<EMAIL>', 'الرياض - حي النرجس', '1234567890', 'individual'),
          (2, 'سارة محمود الأحمد', '0507654321', '<EMAIL>', 'جدة - حي الروضة', '0987654321', 'individual'),
          (3, 'أحمد علي المحمد', '0551234567', '<EMAIL>', 'الدمام - حي الفيصلية', '1122334455', 'individual'),
          (4, 'شركة التقنية المتقدمة', '0112345678', '<EMAIL>', 'الرياض - حي العليا', '7001234567', 'business')`)

  // إدراج الاشتراكات التجريبية
  db.run(`INSERT INTO subscriptions (id, customer_id, plan_id, start_date, end_date, final_price, auto_renew) 
          VALUES 
          (1, 1, 2, '2024-01-01', '2024-02-01', 150.00, 1),
          (2, 2, 3, '2024-01-15', '2024-02-15', 250.00, 1),
          (3, 3, 1, '2023-12-01', '2024-01-01', 100.00, 0),
          (4, 4, 4, '2024-01-10', '2024-02-10', 500.00, 1)`)

  // إدراج المدفوعات التجريبية
  db.run(`INSERT INTO payments (id, customer_id, subscription_id, amount, payment_method, payment_date, receipt_number) 
          VALUES 
          (1, 1, 1, 150.00, 'نقدي', '2024-01-01', 'REC-001'),
          (2, 2, 2, 250.00, 'تحويل بنكي', '2024-01-15', 'REC-002'),
          (3, 3, 3, 100.00, 'بطاقة ائتمان', '2023-12-01', 'REC-003'),
          (4, 4, 4, 500.00, 'شيك', '2024-01-10', 'REC-004')`)

  console.log('✅ تم إنشاء قاعدة البيانات بنجاح')
  console.log('📊 تم إدراج البيانات التجريبية')
  console.log('🔐 بيانات الدخول: admin / admin123')
})

db.close(() => {
  console.log('🎉 تم إغلاق قاعدة البيانات بنجاح')
  console.log('🚀 يمكنك الآن تشغيل الخادم: npm run server')
})
