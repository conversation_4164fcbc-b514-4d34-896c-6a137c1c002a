:root {
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --border-radius: 12px;
  --box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Cairo', sans-serif;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  min-height: 100vh;
  direction: rtl;
  text-align: right;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius);
}

.card-modern {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  border: none;
  transition: var(--transition);
}

.card-modern:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.btn-modern {
  border-radius: var(--border-radius);
  padding: 12px 30px;
  font-weight: 600;
  transition: var(--transition);
  border: none;
}

.btn-primary-modern {
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  color: white;
}

.btn-primary-modern:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.form-control-modern {
  border-radius: var(--border-radius);
  border: 2px solid #e9ecef;
  padding: 12px 15px;
  transition: var(--transition);
}

.form-control-modern:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.navbar-modern {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: var(--box-shadow);
}

.sidebar-modern {
  background: linear-gradient(180deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  min-height: 100vh;
  color: white;
}

.sidebar-item {
  padding: 15px 20px;
  margin: 5px 10px;
  border-radius: var(--border-radius);
  transition: var(--transition);
  cursor: pointer;
}

.sidebar-item:hover {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar-item.active {
  background: rgba(255, 255, 255, 0.2);
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.table-modern {
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.table-modern th {
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  color: white;
  font-weight: 600;
  border: none;
  padding: 15px;
}

.table-modern td {
  padding: 15px;
  border-bottom: 1px solid #f8f9fa;
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.85em;
  font-weight: 600;
}

.status-active {
  background: #d4edda;
  color: #155724;
}

.status-expired {
  background: #f8d7da;
  color: #721c24;
}

.status-pending {
  background: #fff3cd;
  color: #856404;
}

@media (max-width: 768px) {
  .sidebar-modern {
    position: fixed;
    top: 0;
    right: -250px;
    width: 250px;
    z-index: 1000;
    transition: var(--transition);
  }
  
  .sidebar-modern.show {
    right: 0;
  }
}
