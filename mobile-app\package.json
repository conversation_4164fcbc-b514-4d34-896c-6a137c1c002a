{"name": "salam-mobile", "version": "1.0.0", "description": "تطبيق سلام للهاتف المحمول - إدارة اشتراكات الإنترنت", "main": "index.js", "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "start": "react-native start", "test": "jest", "lint": "eslint .", "build-android": "cd android && ./gradlew assembleRelease", "build-apk": "cd android && ./gradlew assembleRelease && echo 'APK created at: android/app/build/outputs/apk/release/app-release.apk'"}, "dependencies": {"react": "18.2.0", "react-native": "0.72.6", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@react-navigation/bottom-tabs": "^6.5.8", "react-native-screens": "^3.22.1", "react-native-safe-area-context": "^4.7.1", "react-native-gesture-handler": "^2.12.1", "react-native-vector-icons": "^10.0.0", "axios": "^1.3.4", "@react-native-async-storage/async-storage": "^1.19.1", "react-native-toast-message": "^2.1.6"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/eslint-config": "^0.72.2", "@react-native/metro-config": "^0.72.11", "@tsconfig/react-native": "^3.0.0", "@types/react": "^18.0.24", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.2.1", "eslint": "^8.19.0", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.76.8", "prettier": "^2.4.1", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "engines": {"node": ">=16"}}