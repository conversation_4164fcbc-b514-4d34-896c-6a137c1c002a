@echo off
echo ========================================
echo    تشغيل تطبيق سلام المحسن
echo ========================================
echo.

echo 🔧 إصلاح قاعدة البيانات...
node fix-database.js

echo.
echo 🚀 تشغيل الخادم...
start "Salam Server" cmd /k "npm run server"

echo.
echo ⏳ انتظار تشغيل الخادم...
timeout /t 3 /nobreak > nul

echo.
echo 🌐 تشغيل الواجهة...
start "Salam Frontend" cmd /k "npm run dev"

echo.
echo ⏳ انتظار تشغيل الواجهة...
timeout /t 5 /nobreak > nul

echo.
echo 🎉 تم تشغيل التطبيق بنجاح!
echo.
echo 📊 الواجهة: http://localhost:3000
echo 🔗 API: http://localhost:5000/api
echo 🔐 بيانات الدخول: admin / admin123
echo.
echo اضغط أي مفتاح لفتح التطبيق في المتصفح...
pause > nul

start http://localhost:3000

echo.
echo ========================================
echo تطبيق سلام يعمل الآن!
echo ========================================
pause
