import axios from 'axios'

console.log('🔧 إصلاح مشكلة تحميل البيانات...')
console.log('================================')

// إعدادات axios
axios.defaults.baseURL = 'http://localhost:5000/api'
axios.defaults.timeout = 10000
axios.defaults.headers.common['Content-Type'] = 'application/json'

async function testConnection() {
  try {
    console.log('🔗 اختبار الاتصال بالخادم...')
    const response = await axios.get('/health')
    console.log('✅ الخادم متصل:', response.data.status)
    return true
  } catch (error) {
    console.log('❌ فشل الاتصال:', error.message)
    return false
  }
}

async function testLogin() {
  try {
    console.log('🔐 اختبار تسجيل الدخول...')
    const response = await axios.post('/auth/login', {
      username: 'admin',
      password: 'admin123'
    })
    
    if (response.data.token) {
      console.log('✅ تسجيل الدخول نجح')
      
      // حفظ التوكن
      axios.defaults.headers.common['Authorization'] = `Bearer ${response.data.token}`
      return response.data.token
    } else {
      throw new Error('لم يتم الحصول على token')
    }
  } catch (error) {
    console.log('❌ فشل تسجيل الدخول:', error.message)
    return null
  }
}

async function testCustomers(token) {
  try {
    console.log('👥 اختبار تحميل العملاء...')
    const response = await axios.get('/customers')
    const customers = response.data.customers || response.data
    console.log(`✅ تم تحميل ${customers.length} عملاء`)
    
    customers.forEach((customer, index) => {
      console.log(`   ${index + 1}. ${customer.name} - ${customer.phone}`)
    })
    
    return true
  } catch (error) {
    console.log('❌ فشل تحميل العملاء:', error.message)
    if (error.response) {
      console.log('   الكود:', error.response.status)
      console.log('   الرسالة:', error.response.data?.message || 'غير محدد')
    }
    return false
  }
}

async function testStats(token) {
  try {
    console.log('📊 اختبار تحميل الإحصائيات...')
    const response = await axios.get('/dashboard/stats')
    console.log('✅ تم تحميل الإحصائيات:', {
      العملاء: response.data.totalCustomers,
      الاشتراكات: response.data.activeSubscriptions,
      الإيرادات: response.data.totalRevenue
    })
    return true
  } catch (error) {
    console.log('❌ فشل تحميل الإحصائيات:', error.message)
    if (error.response) {
      console.log('   الكود:', error.response.status)
      console.log('   الرسالة:', error.response.data?.message || 'غير محدد')
    }
    return false
  }
}

async function runTests() {
  console.log('🚀 بدء اختبار تحميل البيانات...')
  console.log('')
  
  // اختبار الاتصال
  const connected = await testConnection()
  if (!connected) {
    console.log('')
    console.log('💡 تأكد من تشغيل الخادم: npm run server')
    return
  }
  
  console.log('')
  
  // اختبار تسجيل الدخول
  const token = await testLogin()
  if (!token) {
    console.log('')
    console.log('💡 تحقق من بيانات المستخدم في قاعدة البيانات')
    return
  }
  
  console.log('')
  
  // اختبار تحميل العملاء
  const customersOk = await testCustomers(token)
  console.log('')
  
  // اختبار تحميل الإحصائيات
  const statsOk = await testStats(token)
  console.log('')
  
  if (customersOk && statsOk) {
    console.log('🎉 جميع البيانات تحمل بشكل صحيح!')
    console.log('')
    console.log('📱 يمكنك الآن فتح التطبيق على: http://localhost:3000')
    console.log('🔐 بيانات الدخول: admin / admin123')
  } else {
    console.log('⚠️ هناك مشاكل في تحميل بعض البيانات')
    console.log('')
    console.log('🔧 جرب الحلول التالية:')
    console.log('   1. أعد تشغيل الخادم: npm run server')
    console.log('   2. أصلح قاعدة البيانات: node fix-database.js')
    console.log('   3. أعد تشغيل الواجهة: npm run dev')
  }
  
  console.log('')
  console.log('================================')
  console.log('انتهى الاختبار')
}

runTests().catch(console.error)
