@echo off
echo ========================================
echo    إنشاء ملف ZIP للرفع السريع
echo ========================================
echo.

echo جاري إنشاء ملف ZIP من مجلد dist...

if exist "dist" (
    echo ✅ تم العثور على مجلد dist
    
    if exist "salam-app.zip" (
        del "salam-app.zip"
        echo تم حذف الملف القديم
    )
    
    powershell -command "Compress-Archive -Path 'dist\*' -DestinationPath 'salam-app.zip' -Force"
    
    if exist "salam-app.zip" (
        echo ✅ تم إنشاء ملف salam-app.zip بنجاح
        echo.
        echo يمكنك الآن:
        echo 1. رفع salam-app.zip على أي خدمة استضافة
        echo 2. أو استخراج محتوياته ورفعها
        echo.
        echo خدمات الرفع السريع:
        echo - Netlify Drop: https://app.netlify.com/drop
        echo - Surge.sh: surge
        echo - Vercel: vercel
        echo.
        
        for %%A in ("salam-app.zip") do echo حجم الملف: %%~zA bytes
        
        echo.
        echo هل تريد فتح مجلد الملف؟ (y/n)
        set /p choice=
        if /i "%choice%"=="y" (
            start explorer .
        )
    ) else (
        echo ❌ فشل في إنشاء ملف ZIP
    )
) else (
    echo ❌ لم يتم العثور على مجلد dist
    echo يرجى تشغيل: npm run build أولاً
)

echo.
echo ========================================
echo للحصول على رابط فوري:
echo 1. اذهب إلى https://app.netlify.com/drop
echo 2. اسحب محتويات مجلد dist
echo 3. احصل على رابط مثل: https://amazing-name.netlify.app
echo ========================================
pause
