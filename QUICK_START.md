# دليل البدء السريع - سلام

## 🚀 تشغيل المشروع في 3 خطوات

### 1. تثبيت المتطلبات
```bash
npm install
```

### 2. تشغيل النظام
```bash
# تشغيل الخادم والواجهة معاً
npm run dev:full
```

### 3. الوصول للنظام
- **تطبيق الويب**: http://localhost:3000
- **خادم API**: http://localhost:5000

## 🔐 بيانات تسجيل الدخول

```
اسم المستخدم: admin
كلمة المرور: admin123
```

## 📱 تشغيل تطبيق الأندرويد

```bash
cd mobile-app
npm install
npm run android
```

## 📦 بناء APK

```bash
cd mobile-app
npm run build-apk
```

## 🛠️ أوامر مفيدة

```bash
# تشغيل الخادم فقط
npm run server

# تشغيل الواجهة فقط
npm run dev

# بناء الواجهة للإنتاج
npm run build
```

## 📋 قائمة المراجعة

- [ ] تم تثبيت Node.js
- [ ] تم تشغيل `npm install`
- [ ] تم تشغيل `npm run dev:full`
- [ ] تم فتح http://localhost:3000
- [ ] تم تسجيل الدخول بالبيانات التجريبية
- [ ] تم استكشاف لوحة التحكم

## 🎯 الميزات الرئيسية

✅ **لوحة تحكم احترافية**
✅ **إدارة العملاء**
✅ **إدارة الاشتراكات**
✅ **نظام المدفوعات**
✅ **التقارير والإحصائيات**
✅ **تطبيق أندرويد**
✅ **واجهة عربية كاملة**
✅ **تصميم عصري ومتجاوب**

## 🆘 مساعدة سريعة

**مشكلة في التشغيل؟**
1. تأكد من تثبيت Node.js
2. احذف مجلد `node_modules` وشغل `npm install` مرة أخرى
3. تأكد من عدم استخدام المنافذ 3000 و 5000

**لا يظهر التطبيق؟**
- تأكد من فتح http://localhost:3000 في المتصفح
- تحقق من عمل الخادم على المنفذ 5000

---

**🎉 مبروك! نظام سلام جاهز للاستخدام**
