<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سلام - نظام إدارة اشتراكات الإنترنت</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        body {
            background: linear-gradient(135deg, #2563eb 0%, #059669 50%, #0891b2 100%);
            min-height: 100vh;
            color: white;
        }
        .card-modern {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .btn-modern {
            border-radius: 15px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.2);
        }
        .login-form {
            max-width: 400px;
            margin: 0 auto;
        }
        .hidden { display: none; }
        .customer-row {
            background: rgba(255, 255, 255, 0.1);
            margin: 0.5rem 0;
            padding: 1rem;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .customer-row:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-5px);
        }
    </style>
</head>
<body>
    <!-- شاشة تسجيل الدخول -->
    <div id="loginScreen" class="container-fluid d-flex align-items-center justify-content-center min-vh-100">
        <div class="login-form">
            <div class="card-modern p-5 text-center">
                <div class="mb-4">
                    <i class="fas fa-wifi" style="font-size: 4rem; color: #22c55e;"></i>
                </div>
                <h2 class="fw-bold mb-3">سلام</h2>
                <p class="mb-4">نظام إدارة اشتراكات الإنترنت</p>
                
                <form id="loginForm">
                    <div class="mb-3">
                        <input type="text" id="username" class="form-control form-control-lg" 
                               placeholder="اسم المستخدم" value="admin" required>
                    </div>
                    <div class="mb-3">
                        <input type="password" id="password" class="form-control form-control-lg" 
                               placeholder="كلمة المرور" value="admin123" required>
                    </div>
                    <button type="submit" class="btn btn-success btn-modern w-100 btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        تسجيل الدخول
                    </button>
                </form>
                
                <div class="mt-4 p-3" style="background: rgba(255,255,255,0.1); border-radius: 10px;">
                    <small>
                        <i class="fas fa-info-circle me-2"></i>
                        بيانات تجريبية: admin / admin123
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- الشاشة الرئيسية -->
    <div id="mainScreen" class="hidden">
        <!-- شريط التنقل -->
        <nav class="navbar navbar-expand-lg" style="background: rgba(0,0,0,0.2);">
            <div class="container-fluid">
                <a class="navbar-brand text-white fw-bold" href="#">
                    <i class="fas fa-wifi me-2"></i>سلام
                </a>
                <div class="navbar-nav ms-auto">
                    <button class="btn btn-outline-light btn-sm" onclick="logout()">
                        <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                    </button>
                </div>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <div class="container-fluid p-4">
            <!-- الإحصائيات -->
            <div class="row mb-4">
                <div class="col-12">
                    <h3 class="mb-4"><i class="fas fa-chart-bar me-2"></i>لوحة التحكم</h3>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <i class="fas fa-users fa-2x mb-2" style="color: #22c55e;"></i>
                        <h4 id="totalCustomers">0</h4>
                        <p>إجمالي العملاء</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <i class="fas fa-wifi fa-2x mb-2" style="color: #3b82f6;"></i>
                        <h4 id="activeSubscriptions">0</h4>
                        <p>الاشتراكات النشطة</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <i class="fas fa-money-bill-wave fa-2x mb-2" style="color: #f59e0b;"></i>
                        <h4 id="totalRevenue">0</h4>
                        <p>إجمالي الإيرادات</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <i class="fas fa-chart-line fa-2x mb-2" style="color: #06b6d4;"></i>
                        <h4 id="monthlyRevenue">0</h4>
                        <p>الإيرادات الشهرية</p>
                    </div>
                </div>
            </div>

            <!-- العملاء -->
            <div class="row">
                <div class="col-12">
                    <div class="card-modern p-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4><i class="fas fa-users me-2"></i>العملاء</h4>
                            <button class="btn btn-success btn-modern" onclick="showAddCustomer()">
                                <i class="fas fa-plus me-2"></i>إضافة عميل
                            </button>
                        </div>
                        <div id="customersList">
                            <div class="text-center p-4">
                                <i class="fas fa-spinner fa-spin fa-2x mb-3"></i>
                                <p>جاري تحميل العملاء...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let token = null;
        const API_BASE = 'http://localhost:5000/api';

        // تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (data.token) {
                    token = data.token;
                    showMainScreen();
                    loadDashboardData();
                } else {
                    alert('خطأ في تسجيل الدخول');
                }
            } catch (error) {
                alert('خطأ في الاتصال بالخادم');
                console.error(error);
            }
        });

        // عرض الشاشة الرئيسية
        function showMainScreen() {
            document.getElementById('loginScreen').classList.add('hidden');
            document.getElementById('mainScreen').classList.remove('hidden');
        }

        // تحميل بيانات لوحة التحكم
        async function loadDashboardData() {
            try {
                // تحميل الإحصائيات
                const statsResponse = await fetch(`${API_BASE}/dashboard/stats`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                const stats = await statsResponse.json();
                
                document.getElementById('totalCustomers').textContent = stats.totalCustomers || 0;
                document.getElementById('activeSubscriptions').textContent = stats.activeSubscriptions || 0;
                document.getElementById('totalRevenue').textContent = (stats.totalRevenue || 0) + ' ريال';
                document.getElementById('monthlyRevenue').textContent = (stats.monthlyRevenue || 0) + ' ريال';
                
                // تحميل العملاء
                const customersResponse = await fetch(`${API_BASE}/customers`, {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                const customersData = await customersResponse.json();
                const customers = customersData.customers || customersData;
                
                displayCustomers(customers);
                
            } catch (error) {
                console.error('خطأ في تحميل البيانات:', error);
                document.getElementById('customersList').innerHTML = 
                    '<div class="alert alert-danger">خطأ في تحميل البيانات</div>';
            }
        }

        // عرض العملاء
        function displayCustomers(customers) {
            const container = document.getElementById('customersList');
            
            if (customers.length === 0) {
                container.innerHTML = '<div class="text-center p-4">لا توجد عملاء</div>';
                return;
            }
            
            const html = customers.map(customer => `
                <div class="customer-row">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <h6 class="mb-1">${customer.name}</h6>
                            <small class="text-white-50">${customer.customer_type === 'business' ? 'شركة' : 'فرد'}</small>
                        </div>
                        <div class="col-md-3">
                            <i class="fas fa-phone me-2"></i>${customer.phone}
                        </div>
                        <div class="col-md-3">
                            <i class="fas fa-envelope me-2"></i>${customer.email || 'غير محدد'}
                        </div>
                        <div class="col-md-3 text-end">
                            <span class="badge bg-success">${customer.status === 'active' ? 'نشط' : 'معلق'}</span>
                        </div>
                    </div>
                </div>
            `).join('');
            
            container.innerHTML = html;
        }

        // إضافة عميل جديد
        function showAddCustomer() {
            const name = prompt('اسم العميل:');
            const phone = prompt('رقم الهاتف:');
            const email = prompt('البريد الإلكتروني (اختياري):');
            
            if (name && phone) {
                addCustomer({ name, phone, email });
            }
        }

        async function addCustomer(customerData) {
            try {
                const response = await fetch(`${API_BASE}/customers`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify(customerData)
                });
                
                if (response.ok) {
                    alert('تم إضافة العميل بنجاح');
                    loadDashboardData(); // إعادة تحميل البيانات
                } else {
                    alert('خطأ في إضافة العميل');
                }
            } catch (error) {
                alert('خطأ في الاتصال');
                console.error(error);
            }
        }

        // تسجيل الخروج
        function logout() {
            token = null;
            document.getElementById('mainScreen').classList.add('hidden');
            document.getElementById('loginScreen').classList.remove('hidden');
        }
    </script>
</body>
</html>
