import React, { useState, useEffect } from 'react'

const Payments = () => {
  const [payments, setPayments] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchPayments()
  }, [])

  const fetchPayments = async () => {
    try {
      const mockPayments = [
        {
          id: 1,
          customerName: 'محمد أحمد',
          amount: 150,
          date: '2024-01-15',
          method: 'نقدي',
          status: 'مكتمل'
        },
        {
          id: 2,
          customerName: 'سارة محمود',
          amount: 250,
          date: '2024-01-14',
          method: 'تحويل بنكي',
          status: 'مكتمل'
        },
        {
          id: 3,
          customerName: 'أحمد علي',
          amount: 100,
          date: '2024-01-13',
          method: 'نقدي',
          status: 'معلق'
        }
      ]
      setPayments(mockPayments)
    } catch (error) {
      console.error('Error fetching payments:', error)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="text-center">
          <div className="loading-spinner mb-3"></div>
          <h5>جاري تحميل بيانات المدفوعات...</h5>
        </div>
      </div>
    )
  }

  return (
    <div className="fade-in">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="fw-bold text-dark mb-1">إدارة المدفوعات</h2>
          <p className="text-muted mb-0">تتبع وإدارة مدفوعات العملاء</p>
        </div>
        <button className="btn btn-primary-modern">
          <i className="fas fa-plus me-2"></i>
          تسجيل دفعة جديدة
        </button>
      </div>

      <div className="card-modern">
        <div className="card-body p-0">
          <div className="table-responsive">
            <table className="table table-modern mb-0">
              <thead>
                <tr>
                  <th>العميل</th>
                  <th>المبلغ</th>
                  <th>التاريخ</th>
                  <th>طريقة الدفع</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {payments.map((payment) => (
                  <tr key={payment.id}>
                    <td>{payment.customerName}</td>
                    <td>{payment.amount} ريال</td>
                    <td>{payment.date}</td>
                    <td>{payment.method}</td>
                    <td>
                      <span className={`status-badge ${payment.status === 'مكتمل' ? 'status-active' : 'status-pending'}`}>
                        {payment.status}
                      </span>
                    </td>
                    <td>
                      <div className="btn-group">
                        <button className="btn btn-sm btn-outline-primary">
                          <i className="fas fa-eye"></i>
                        </button>
                        <button className="btn btn-sm btn-outline-success">
                          <i className="fas fa-print"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Payments
