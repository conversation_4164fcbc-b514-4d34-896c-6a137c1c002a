# 🌐 رفع تطبيق سلام الآن - احصل على رابط فوري!

## ✅ الملفات جاهزة للرفع!

مجلد `dist` يحتوي على التطبيق المبني وجاهز للنشر.

## 🚀 طريقة سريعة للحصول على رابط (دقيقتان):

### الخطوة 1: اذهب إلى Netlify Drop
- **الرابط المباشر**: https://app.netlify.com/drop
- لا تحتاج لإنشاء حساب!

### الخطوة 2: اسحب مجلد dist
1. افتح مجلد `dist` في مستكشف الملفات
2. حدد جميع الملفات داخل مجلد `dist`:
   - index.html
   - مجلد assets
   - manifest.json
   - sw.js
   - icon-generator.html
3. اسحبهم إلى صفحة Netlify Drop

### الخطوة 3: انتظر النشر
- سيستغرق 30-60 ثانية
- ستحصل على رابط مثل: `https://amazing-name-123456.netlify.app`

## 🔗 طرق أخرى للحصول على رابط:

### طريقة 1: Surge.sh (سريع جداً)
```bash
# ثبت surge
npm install -g surge

# ادخل مجلد dist
cd dist

# ارفع
surge

# ستحصل على رابط مثل: https://salam-app.surge.sh
```

### طريقة 2: Vercel (احترافي)
```bash
# ثبت vercel
npm install -g vercel

# ارفع
vercel --prod

# ستحصل على رابط مثل: https://salam-app.vercel.app
```

### طريقة 3: GitHub Pages
1. أنشئ repository جديد على GitHub
2. ارفع جميع الملفات
3. فعل GitHub Pages في الإعدادات
4. ستحصل على رابط مثل: https://username.github.io/repo-name

## 📱 بعد الحصول على الرابط:

### تحديث تطبيق الأندرويد:
1. عدل الرابط في `android-app/app/src/main/java/.../MainActivity.java`
2. استبدل `http://10.0.2.2:3001` بالرابط الجديد
3. ابني APK جديد

## 🔐 بيانات تسجيل الدخول:
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123

## ⚡ الطريقة الأسرع الآن:

### استخدم Netlify Drop:
1. اذهب إلى: https://app.netlify.com/drop
2. اسحب محتويات مجلد `dist`
3. احصل على رابط فوري!

---

**🎯 الهدف: الحصول على رابط مثل https://your-app.netlify.app في أقل من دقيقتين!**
