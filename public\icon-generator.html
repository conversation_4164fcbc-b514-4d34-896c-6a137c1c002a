<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مولد أيقونة سلام</title>
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        .icon-container {
            text-align: center;
        }
        .icon {
            width: 512px;
            height: 512px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 80px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            margin: 20px auto;
        }
        .icon i {
            font-size: 200px;
            margin-bottom: 20px;
        }
        .icon h1 {
            font-size: 80px;
            margin: 0;
            font-weight: bold;
        }
        .icon-small {
            width: 192px;
            height: 192px;
            border-radius: 30px;
        }
        .icon-small i {
            font-size: 80px;
            margin-bottom: 10px;
        }
        .icon-small h1 {
            font-size: 32px;
        }
        button {
            background: white;
            color: #667eea;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
        }
    </style>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="icon-container">
        <h2 style="color: white; text-align: center;">أيقونات تطبيق سلام</h2>
        
        <!-- أيقونة 512x512 -->
        <div class="icon" id="icon512">
            <i class="fas fa-wifi"></i>
            <h1>سلام</h1>
        </div>
        <button onclick="downloadIcon('icon512', 'icon-512.png')">تحميل أيقونة 512x512</button>
        
        <!-- أيقونة 192x192 -->
        <div class="icon icon-small" id="icon192">
            <i class="fas fa-wifi"></i>
            <h1>سلام</h1>
        </div>
        <button onclick="downloadIcon('icon192', 'icon-192.png')">تحميل أيقونة 192x192</button>
    </div>

    <script src="https://html2canvas.hertzen.com/dist/html2canvas.min.js"></script>
    <script>
        function downloadIcon(elementId, filename) {
            const element = document.getElementById(elementId);
            html2canvas(element, {
                backgroundColor: null,
                scale: 2
            }).then(canvas => {
                const link = document.createElement('a');
                link.download = filename;
                link.href = canvas.toDataURL();
                link.click();
            });
        }
    </script>
</body>
</html>
