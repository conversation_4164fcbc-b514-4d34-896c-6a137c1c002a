# تطبيق سلام للهاتف المحمول

تطبيق الأندرويد الخاص بنظام إدارة اشتراكات الإنترنت "سلام".

## المميزات

- **تسجيل دخول آمن** مع حفظ الجلسة
- **لوحة تحكم محمولة** مع الإحصائيات الأساسية
- **واجهة عربية** مناسبة للهواتف المحمولة
- **تصميم عصري** مع Material Design
- **إشعارات** للعمليات المختلفة

## متطلبات التطوير

- Node.js (الإصدار 16 أو أحدث)
- React Native CLI
- Android Studio (للأندرويد)
- Xcode (للآيفون - macOS فقط)

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
cd mobile-app
npm install
```

### 2. تشغيل التطبيق

#### للأندرويد:
```bash
# تأكد من تشغيل Android emulator أو توصيل جهاز أندرويد
npm run android
```

#### للآيفون (macOS فقط):
```bash
npm run ios
```

### 3. بناء APK للأندرويد
```bash
# بناء APK للإنتاج
npm run build-apk
```

سيتم إنشاء ملف APK في المسار:
`android/app/build/outputs/apk/release/app-release.apk`

## إعداد الخادم

تأكد من تشغيل خادم API على العنوان المناسب:

- **للمحاكي الأندرويد**: `http://********:5000`
- **للجهاز الفعلي**: `http://[IP_ADDRESS]:5000`

## الاستخدام

1. **تسجيل الدخول**: استخدم البيانات التجريبية:
   - اسم المستخدم: `admin`
   - كلمة المرور: `admin123`

2. **لوحة التحكم**: عرض الإحصائيات الأساسية

3. **الإجراءات السريعة**: الوصول السريع للوظائف الأساسية

## ملاحظات مهمة

- تأكد من تشغيل خادم API قبل استخدام التطبيق
- للاستخدام على جهاز فعلي، قم بتحديث `API_BASE_URL` في `App.js`
- التطبيق يحتاج إلى إذن الإنترنت للعمل

## الملفات المهمة

- `App.js` - المكون الرئيسي للتطبيق
- `package.json` - إعدادات المشروع والمتطلبات
- `android/` - ملفات مشروع الأندرويد
- `ios/` - ملفات مشروع الآيفون

## استكشاف الأخطاء

### مشاكل شائعة:

1. **خطأ في الاتصال بالخادم**:
   - تأكد من تشغيل خادم API
   - تحقق من عنوان IP الصحيح

2. **مشاكل في البناء**:
   - تأكد من تثبيت جميع المتطلبات
   - نظف المشروع: `npx react-native clean`

3. **مشاكل الأيقونات**:
   - تأكد من ربط مكتبة الأيقونات بشكل صحيح

## التطوير المستقبلي

- [ ] إضافة المزيد من الشاشات (العملاء، الاشتراكات، إلخ)
- [ ] نظام الإشعارات المحلية
- [ ] وضع عدم الاتصال
- [ ] مزامنة البيانات
- [ ] تحسين الأداء

---

**© 2024 سلام - تطبيق إدارة اشتراكات الإنترنت**
