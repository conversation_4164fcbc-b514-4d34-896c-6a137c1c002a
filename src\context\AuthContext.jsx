import React, { createContext, useContext, useState, useEffect } from 'react'
import axios from 'axios'
import { toast } from 'react-toastify'

const AuthContext = createContext()

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)

  // Configure axios defaults
  const API_BASE_URL = 'http://localhost:5000/api'  // دائماً للتطوير المحلي

  axios.defaults.baseURL = API_BASE_URL
  axios.defaults.timeout = 10000  // 10 ثواني timeout

  // إعدادات CORS
  axios.defaults.headers.common['Content-Type'] = 'application/json'
  axios.defaults.withCredentials = false
  
  // Add token to requests if available
  useEffect(() => {
    const token = localStorage.getItem('token')
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
      checkAuth()
    } else {
      setLoading(false)
    }
  }, [])

  const checkAuth = async () => {
    try {
      const response = await axios.get('/auth/me')
      setUser(response.data.user)
    } catch (error) {
      console.error('Auth check failed:', error)
      localStorage.removeItem('token')
      delete axios.defaults.headers.common['Authorization']
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  const login = async (username, password) => {
    try {
      const response = await axios.post('/auth/login', { username, password })
      const { token, user } = response.data
      
      localStorage.setItem('token', token)
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
      setUser(user)
      
      toast.success('تم تسجيل الدخول بنجاح')
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.message || 'خطأ في تسجيل الدخول'
      toast.error(message)
      return { success: false, message }
    }
  }

  const logout = () => {
    localStorage.removeItem('token')
    delete axios.defaults.headers.common['Authorization']
    setUser(null)
    toast.info('تم تسجيل الخروج')
  }

  const register = async (userData) => {
    try {
      const response = await axios.post('/auth/register', userData)
      toast.success('تم إنشاء الحساب بنجاح')
      return { success: true }
    } catch (error) {
      const message = error.response?.data?.message || 'خطأ في إنشاء الحساب'
      toast.error(message)
      return { success: false, message }
    }
  }

  const value = {
    user,
    loading,
    login,
    logout,
    register
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
