<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تطبيق سلام</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #2563eb 0%, #059669 50%, #0891b2 100%);
            min-height: 100vh;
            color: white;
        }
        .test-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 2rem;
            margin: 1rem 0;
        }
        .status-ok { color: #22c55e; }
        .status-error { color: #ef4444; }
        .status-warning { color: #f59e0b; }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="text-center mb-5">
                    <h1><i class="fas fa-wifi me-3"></i>اختبار تطبيق سلام</h1>
                    <p class="lead">فحص شامل لجميع مكونات التطبيق</p>
                </div>

                <div class="test-card">
                    <h3><i class="fas fa-server me-2"></i>حالة الخادم</h3>
                    <div id="server-status">
                        <i class="fas fa-spinner fa-spin"></i> جاري الفحص...
                    </div>
                </div>

                <div class="test-card">
                    <h3><i class="fas fa-key me-2"></i>تسجيل الدخول</h3>
                    <div id="login-status">
                        <i class="fas fa-spinner fa-spin"></i> جاري الفحص...
                    </div>
                </div>

                <div class="test-card">
                    <h3><i class="fas fa-database me-2"></i>قاعدة البيانات</h3>
                    <div id="database-status">
                        <i class="fas fa-spinner fa-spin"></i> جاري الفحص...
                    </div>
                </div>

                <div class="test-card">
                    <h3><i class="fas fa-chart-bar me-2"></i>الإحصائيات</h3>
                    <div id="stats-status">
                        <i class="fas fa-spinner fa-spin"></i> جاري الفحص...
                    </div>
                </div>

                <div class="test-card">
                    <h3><i class="fas fa-cog me-2"></i>الإجراءات</h3>
                    <div class="d-grid gap-2">
                        <button class="btn btn-success" onclick="openApp()">
                            <i class="fas fa-external-link-alt me-2"></i>
                            فتح التطبيق
                        </button>
                        <button class="btn btn-info" onclick="runTests()">
                            <i class="fas fa-sync-alt me-2"></i>
                            إعادة الفحص
                        </button>
                        <button class="btn btn-warning" onclick="fixDatabase()">
                            <i class="fas fa-wrench me-2"></i>
                            إصلاح قاعدة البيانات
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let token = null;

        async function checkServer() {
            try {
                const response = await fetch('http://localhost:5000/api/health');
                const data = await response.json();
                document.getElementById('server-status').innerHTML = 
                    `<i class="fas fa-check-circle status-ok"></i> الخادم يعمل بشكل صحيح<br>
                     <small>الحالة: ${data.status} | الوقت: ${new Date(data.timestamp).toLocaleString('ar')}</small>`;
                return true;
            } catch (error) {
                document.getElementById('server-status').innerHTML = 
                    `<i class="fas fa-times-circle status-error"></i> الخادم لا يعمل<br>
                     <small>الخطأ: ${error.message}</small><br>
                     <small class="status-warning">💡 شغل الأمر: npm run server</small>`;
                return false;
            }
        }

        async function checkLogin() {
            try {
                const response = await fetch('http://localhost:5000/api/auth/login', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ username: 'admin', password: 'admin123' })
                });
                const data = await response.json();
                if (data.token) {
                    token = data.token;
                    document.getElementById('login-status').innerHTML = 
                        `<i class="fas fa-check-circle status-ok"></i> تسجيل الدخول يعمل<br>
                         <small>المستخدم: ${data.user.name}</small>`;
                    return true;
                } else {
                    throw new Error('لم يتم الحصول على token');
                }
            } catch (error) {
                document.getElementById('login-status').innerHTML = 
                    `<i class="fas fa-times-circle status-error"></i> مشكلة في تسجيل الدخول<br>
                     <small>الخطأ: ${error.message}</small>`;
                return false;
            }
        }

        async function checkDatabase() {
            if (!token) {
                document.getElementById('database-status').innerHTML = 
                    `<i class="fas fa-exclamation-triangle status-warning"></i> يحتاج تسجيل دخول أولاً`;
                return false;
            }

            try {
                const response = await fetch('http://localhost:5000/api/customers', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                const data = await response.json();
                const customers = data.customers || data;
                document.getElementById('database-status').innerHTML = 
                    `<i class="fas fa-check-circle status-ok"></i> قاعدة البيانات تعمل<br>
                     <small>عدد العملاء: ${customers.length}</small>`;
                return true;
            } catch (error) {
                document.getElementById('database-status').innerHTML = 
                    `<i class="fas fa-times-circle status-error"></i> مشكلة في قاعدة البيانات<br>
                     <small>الخطأ: ${error.message}</small>`;
                return false;
            }
        }

        async function checkStats() {
            if (!token) {
                document.getElementById('stats-status').innerHTML = 
                    `<i class="fas fa-exclamation-triangle status-warning"></i> يحتاج تسجيل دخول أولاً`;
                return false;
            }

            try {
                const response = await fetch('http://localhost:5000/api/dashboard/stats', {
                    headers: { 'Authorization': `Bearer ${token}` }
                });
                const data = await response.json();
                document.getElementById('stats-status').innerHTML = 
                    `<i class="fas fa-check-circle status-ok"></i> الإحصائيات تعمل<br>
                     <small>العملاء: ${data.totalCustomers} | الاشتراكات: ${data.activeSubscriptions}</small>`;
                return true;
            } catch (error) {
                document.getElementById('stats-status').innerHTML = 
                    `<i class="fas fa-times-circle status-error"></i> مشكلة في الإحصائيات<br>
                     <small>الخطأ: ${error.message}</small>`;
                return false;
            }
        }

        async function runTests() {
            document.querySelectorAll('[id$="-status"]').forEach(el => {
                el.innerHTML = '<i class="fas fa-spinner fa-spin"></i> جاري الفحص...';
            });

            const serverOk = await checkServer();
            if (serverOk) {
                const loginOk = await checkLogin();
                if (loginOk) {
                    await checkDatabase();
                    await checkStats();
                }
            }
        }

        function openApp() {
            window.open('http://localhost:3000', '_blank');
        }

        function fixDatabase() {
            alert('شغل الأمر التالي في Terminal:\nnode fix-database.js');
        }

        // تشغيل الفحص عند تحميل الصفحة
        window.onload = runTests;
    </script>
</body>
</html>
