<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>سلام - نظام إدارة اشتراكات الإنترنت</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        body {
            background: linear-gradient(135deg, #2563eb 0%, #059669 50%, #0891b2 100%);
            min-height: 100vh;
            color: white;
        }
        .card-modern {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        .btn-modern {
            border-radius: 15px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-modern:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        .stat-card {
            background: rgba(255, 255, 255, 0.15);
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.2);
        }
        .login-form {
            max-width: 400px;
            margin: 0 auto;
        }
        .hidden { display: none; }
        .customer-row {
            background: rgba(255, 255, 255, 0.1);
            margin: 0.5rem 0;
            padding: 1rem;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        .customer-row:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-5px);
        }
        .demo-mode {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.5);
            border-radius: 10px;
            padding: 1rem;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <!-- شاشة تسجيل الدخول -->
    <div id="loginScreen" class="container-fluid d-flex align-items-center justify-content-center min-vh-100">
        <div class="login-form">
            <div class="card-modern p-5 text-center">
                <div class="mb-4">
                    <i class="fas fa-wifi" style="font-size: 4rem; color: #22c55e;"></i>
                </div>
                <h2 class="fw-bold mb-3">سلام</h2>
                <p class="mb-4">نظام إدارة اشتراكات الإنترنت</p>
                
                <!-- وضع العرض التوضيحي -->
                <div class="demo-mode mb-4">
                    <h6><i class="fas fa-info-circle me-2"></i>وضع العرض التوضيحي</h6>
                    <p class="mb-2">هذا عرض توضيحي للتطبيق مع بيانات تجريبية</p>
                    <small>للحصول على النسخة الكاملة مع قاعدة بيانات حقيقية، تواصل معنا</small>
                </div>
                
                <form id="loginForm">
                    <div class="mb-3">
                        <input type="text" id="username" class="form-control form-control-lg" 
                               placeholder="اسم المستخدم" value="admin" required>
                    </div>
                    <div class="mb-3">
                        <input type="password" id="password" class="form-control form-control-lg" 
                               placeholder="كلمة المرور" value="admin123" required>
                    </div>
                    <button type="submit" class="btn btn-success btn-modern w-100 btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>
                        دخول العرض التوضيحي
                    </button>
                </form>
                
                <div class="mt-4 p-3" style="background: rgba(255,255,255,0.1); border-radius: 10px;">
                    <small>
                        <i class="fas fa-key me-2"></i>
                        بيانات العرض: admin / admin123
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- الشاشة الرئيسية -->
    <div id="mainScreen" class="hidden">
        <!-- شريط التنقل -->
        <nav class="navbar navbar-expand-lg" style="background: rgba(0,0,0,0.2);">
            <div class="container-fluid">
                <a class="navbar-brand text-white fw-bold" href="#">
                    <i class="fas fa-wifi me-2"></i>سلام - عرض توضيحي
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="badge bg-warning me-3 align-self-center">وضع العرض</span>
                    <button class="btn btn-outline-light btn-sm" onclick="logout()">
                        <i class="fas fa-sign-out-alt me-2"></i>خروج
                    </button>
                </div>
            </div>
        </nav>

        <!-- المحتوى الرئيسي -->
        <div class="container-fluid p-4">
            <!-- الإحصائيات -->
            <div class="row mb-4">
                <div class="col-12">
                    <h3 class="mb-4"><i class="fas fa-chart-bar me-2"></i>لوحة التحكم - عرض توضيحي</h3>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <i class="fas fa-users fa-2x mb-2" style="color: #22c55e;"></i>
                        <h4>156</h4>
                        <p>إجمالي العملاء</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <i class="fas fa-wifi fa-2x mb-2" style="color: #3b82f6;"></i>
                        <h4>142</h4>
                        <p>الاشتراكات النشطة</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <i class="fas fa-money-bill-wave fa-2x mb-2" style="color: #f59e0b;"></i>
                        <h4>45,000 ريال</h4>
                        <p>إجمالي الإيرادات</p>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card text-center">
                        <i class="fas fa-chart-line fa-2x mb-2" style="color: #06b6d4;"></i>
                        <h4>12,500 ريال</h4>
                        <p>الإيرادات الشهرية</p>
                    </div>
                </div>
            </div>

            <!-- العملاء -->
            <div class="row">
                <div class="col-12">
                    <div class="card-modern p-4">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h4><i class="fas fa-users me-2"></i>العملاء - بيانات تجريبية</h4>
                            <button class="btn btn-success btn-modern" onclick="showAddCustomer()">
                                <i class="fas fa-plus me-2"></i>إضافة عميل (تجريبي)
                            </button>
                        </div>
                        <div id="customersList">
                            <!-- بيانات تجريبية -->
                            <div class="customer-row">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <h6 class="mb-1">محمد أحمد السالم</h6>
                                        <small class="text-white-50">فرد</small>
                                    </div>
                                    <div class="col-md-3">
                                        <i class="fas fa-phone me-2"></i>0501234567
                                    </div>
                                    <div class="col-md-3">
                                        <i class="fas fa-envelope me-2"></i><EMAIL>
                                    </div>
                                    <div class="col-md-3 text-end">
                                        <span class="badge bg-success">نشط</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="customer-row">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <h6 class="mb-1">سارة محمود الأحمد</h6>
                                        <small class="text-white-50">فرد</small>
                                    </div>
                                    <div class="col-md-3">
                                        <i class="fas fa-phone me-2"></i>0507654321
                                    </div>
                                    <div class="col-md-3">
                                        <i class="fas fa-envelope me-2"></i><EMAIL>
                                    </div>
                                    <div class="col-md-3 text-end">
                                        <span class="badge bg-success">نشط</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="customer-row">
                                <div class="row align-items-center">
                                    <div class="col-md-3">
                                        <h6 class="mb-1">شركة التقنية المتقدمة</h6>
                                        <small class="text-white-50">شركة</small>
                                    </div>
                                    <div class="col-md-3">
                                        <i class="fas fa-phone me-2"></i>0112345678
                                    </div>
                                    <div class="col-md-3">
                                        <i class="fas fa-envelope me-2"></i><EMAIL>
                                    </div>
                                    <div class="col-md-3 text-end">
                                        <span class="badge bg-success">نشط</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- معلومات التواصل -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card-modern p-4 text-center">
                        <h5><i class="fas fa-envelope me-2"></i>للحصول على النسخة الكاملة</h5>
                        <p class="mb-3">هذا عرض توضيحي للتطبيق. للحصول على النسخة الكاملة مع قاعدة بيانات حقيقية وجميع الميزات:</p>
                        <div class="row">
                            <div class="col-md-4">
                                <i class="fas fa-phone fa-2x mb-2" style="color: #22c55e;"></i>
                                <p>اتصل بنا<br><strong>+966 50 123 4567</strong></p>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-envelope fa-2x mb-2" style="color: #3b82f6;"></i>
                                <p>راسلنا<br><strong><EMAIL></strong></p>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-globe fa-2x mb-2" style="color: #f59e0b;"></i>
                                <p>موقعنا<br><strong>www.salam-internet.com</strong></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // تسجيل الدخول
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            if (username === 'admin' && password === 'admin123') {
                showMainScreen();
            } else {
                alert('بيانات الدخول غير صحيحة. استخدم: admin / admin123');
            }
        });

        // عرض الشاشة الرئيسية
        function showMainScreen() {
            document.getElementById('loginScreen').classList.add('hidden');
            document.getElementById('mainScreen').classList.remove('hidden');
        }

        // إضافة عميل جديد (تجريبي)
        function showAddCustomer() {
            alert('هذه ميزة تجريبية. في النسخة الكاملة يمكنك إضافة عملاء حقيقيين إلى قاعدة البيانات.');
        }

        // تسجيل الخروج
        function logout() {
            document.getElementById('mainScreen').classList.add('hidden');
            document.getElementById('loginScreen').classList.remove('hidden');
        }
    </script>
</body>
</html>
