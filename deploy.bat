@echo off
echo ========================================
echo    رفع تطبيق سلام على الإنترنت
echo ========================================
echo.

echo [1/3] بناء التطبيق للإنتاج...
call npm run build
if %errorlevel% neq 0 (
    echo خطأ في بناء التطبيق!
    pause
    exit /b 1
)
echo ✅ تم بناء التطبيق بنجاح

echo.
echo [2/3] التحقق من ملفات النشر...
if exist "dist\index.html" (
    echo ✅ ملفات النشر جاهزة في مجلد dist
) else (
    echo ❌ خطأ: لم يتم العثور على ملفات النشر
    pause
    exit /b 1
)

echo.
echo [3/3] خيارات النشر المتاحة:
echo.
echo 1. Netlify (الأسرع):
echo    - اذهب إلى https://netlify.com/
echo    - اسحب مجلد dist إلى الصفحة
echo.
echo 2. Vercel (الأفضل):
echo    - اذهب إلى https://vercel.com/
echo    - استورد من GitHub
echo.
echo 3. GitHub Pages:
echo    - ارفع الملفات على GitHub
echo    - فعل Pages في الإعدادات
echo.
echo 4. Firebase:
echo    - npm install -g firebase-tools
echo    - firebase login
echo    - firebase init hosting
echo    - firebase deploy
echo.

echo ========================================
echo ملفات جاهزة للنشر:
echo - dist/ (مجلد التطبيق المبني)
echo - api/index.js (Serverless API)
echo - netlify.toml (إعدادات Netlify)
echo - vercel.json (إعدادات Vercel)
echo ========================================
echo.
echo 🌐 بعد النشر، عدل رابط الخادم في:
echo android-app/app/src/main/java/.../MainActivity.java
echo.
echo 📱 ثم ابني APK جديد للأندرويد
echo ========================================

echo.
echo هل تريد فتح مجلد dist؟ (y/n)
set /p choice=
if /i "%choice%"=="y" (
    start explorer dist
)

echo.
echo هل تريد فتح دليل النشر؟ (y/n)
set /p choice2=
if /i "%choice2%"=="y" (
    start DEPLOY_GUIDE.md
)

pause
