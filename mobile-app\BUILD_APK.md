# دليل بناء APK - تطبيق سلام

## الطريقة الأولى: استخدام React Native (الطريقة الاحترافية)

### المتطلبات:
1. **Android Studio** مثبت ومُعد
2. **Java Development Kit (JDK)** الإصدار 11 أو أحدث
3. **Android SDK** مع API Level 33
4. **React Native CLI** مثبت عالمياً

### خطوات التثبيت:

#### 1. تثبيت React Native CLI:
```bash
npm install -g react-native-cli
```

#### 2. تثبيت متطلبات المشروع:
```bash
cd mobile-app
npm install
```

#### 3. إنشاء مشروع React Native جديد:
```bash
# في المجلد الرئيسي
npx react-native init SalamMobile --template react-native-template-typescript
```

#### 4. نسخ الملفات:
```bash
# انسخ محتوى App.js إلى المشروع الجديد
# انسخ package.json dependencies
```

#### 5. بناء APK:
```bash
cd SalamMobile
cd android
./gradlew assembleRelease
```

## الطريقة الثانية: استخدام Expo (الطريقة السريعة)

### 1. تثبيت Expo CLI:
```bash
npm install -g @expo/cli
```

### 2. إنشاء مشروع Expo:
```bash
npx create-expo-app SalamMobileExpo
cd SalamMobileExpo
```

### 3. تثبيت المتطلبات:
```bash
npm install axios @react-native-async-storage/async-storage
npx expo install expo-font
```

### 4. بناء APK:
```bash
npx expo build:android
# أو للإصدار الجديد
eas build --platform android
```

## الطريقة الثالثة: PWA إلى APK (الأسرع)

### استخدام أدوات تحويل PWA:

#### 1. PWA Builder من Microsoft:
- اذهب إلى: https://www.pwabuilder.com/
- أدخل رابط التطبيق: http://localhost:3000
- اختر "Android Package"
- حمل APK

#### 2. Capacitor:
```bash
npm install -g @capacitor/cli
npx cap init SalamApp com.salam.app
npm install @capacitor/android
npx cap add android
npx cap run android
```

## الطريقة الرابعة: استخدام Cordova

### 1. تثبيت Cordova:
```bash
npm install -g cordova
```

### 2. إنشاء مشروع:
```bash
cordova create SalamCordova com.salam.app "سلام"
cd SalamCordova
cordova platform add android
```

### 3. بناء APK:
```bash
cordova build android --release
```

## ملف APK جاهز للتحميل

لتوفير الوقت، يمكنك استخدام الطرق التالية للحصول على APK فوراً:

### الطريقة السريعة - WebView APK:

1. **استخدم Android Studio**:
   - أنشئ مشروع Android جديد
   - أضف WebView يشير إلى http://localhost:3000
   - ابني APK

2. **استخدم أدوات أونلاين**:
   - Website 2 APK Builder
   - AppsGeyser
   - Appy Pie

### مثال كود WebView APK:

```java
// MainActivity.java
public class MainActivity extends AppCompatActivity {
    private WebView webView;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);
        
        webView = findViewById(R.id.webview);
        webView.getSettings().setJavaScriptEnabled(true);
        webView.loadUrl("http://********:3000"); // للمحاكي
        // webView.loadUrl("http://YOUR_IP:3000"); // للجهاز الفعلي
    }
}
```

## تعليمات مهمة:

1. **للاستخدام المحلي**: تأكد من تشغيل خادم التطبيق
2. **للإنتاج**: ارفع التطبيق على خادم ويب
3. **الأمان**: أضف HTTPS للإنتاج
4. **الأيقونة**: أضف أيقونة مخصصة للتطبيق

## ملفات APK جاهزة:

سأقوم بإنشاء APK بسيط باستخدام WebView في الخطوة التالية...

---

**ملاحظة**: الطريقة الأسرع هي استخدام PWA Builder أو إنشاء WebView APK بسيط.
