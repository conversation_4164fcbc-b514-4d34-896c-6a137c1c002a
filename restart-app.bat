@echo off
echo ========================================
echo    إعادة تشغيل تطبيق سلام
echo ========================================
echo.

echo 🛑 إيقاف العمليات الحالية...
taskkill /f /im node.exe 2>nul
npx kill-port 3000 2>nul
npx kill-port 5000 2>nul

echo.
echo ⏳ انتظار 3 ثواني...
timeout /t 3 /nobreak > nul

echo.
echo 🔧 إصلاح قاعدة البيانات...
node fix-database.js

echo.
echo 🧪 اختبار تحميل البيانات...
node fix-data-loading.js

echo.
echo 🚀 تشغيل الخادم...
start "Salam Server" cmd /k "npm run server"

echo.
echo ⏳ انتظار تشغيل الخادم...
timeout /t 5 /nobreak > nul

echo.
echo 🌐 تشغيل الواجهة...
start "Salam Frontend" cmd /k "npm run dev"

echo.
echo ⏳ انتظار تشغيل الواجهة...
timeout /t 5 /nobreak > nul

echo.
echo 🎉 تم إعادة تشغيل التطبيق بنجاح!
echo.
echo 📊 الواجهة: http://localhost:3000
echo 🔗 API: http://localhost:5000/api
echo 🔐 بيانات الدخول: admin / admin123
echo.
echo اضغط أي مفتاح لفتح التطبيق...
pause > nul

start http://localhost:3000

echo.
echo ========================================
echo تطبيق سلام يعمل الآن!
echo ========================================
pause
