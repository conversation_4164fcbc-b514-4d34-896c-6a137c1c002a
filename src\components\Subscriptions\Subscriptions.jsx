import React, { useState, useEffect } from 'react'
import { toast } from 'react-toastify'

const Subscriptions = () => {
  const [subscriptions, setSubscriptions] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    fetchSubscriptions()
  }, [])

  const fetchSubscriptions = async () => {
    try {
      // Simulate API call
      const mockSubscriptions = [
        {
          id: 1,
          customerName: 'محمد أحمد',
          plan: 'باقة 100 ميجا',
          price: 150,
          startDate: '2024-01-01',
          endDate: '2024-02-01',
          status: 'نشط'
        },
        {
          id: 2,
          customerName: 'سارة محمود',
          plan: 'باقة 200 ميجا',
          price: 250,
          startDate: '2024-01-15',
          endDate: '2024-02-15',
          status: 'نشط'
        },
        {
          id: 3,
          customerName: 'أحمد علي',
          plan: 'باقة 50 ميجا',
          price: 100,
          startDate: '2023-12-01',
          endDate: '2024-01-01',
          status: 'منتهي'
        }
      ]
      setSubscriptions(mockSubscriptions)
    } catch (error) {
      toast.error('خطأ في تحميل بيانات الاشتراكات')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'نشط': return 'status-active'
      case 'منتهي': return 'status-expired'
      default: return 'status-pending'
    }
  }

  if (loading) {
    return (
      <div className="d-flex justify-content-center align-items-center" style={{ height: '400px' }}>
        <div className="text-center">
          <div className="loading-spinner mb-3"></div>
          <h5>جاري تحميل بيانات الاشتراكات...</h5>
        </div>
      </div>
    )
  }

  return (
    <div className="fade-in">
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <h2 className="fw-bold text-dark mb-1">إدارة الاشتراكات</h2>
          <p className="text-muted mb-0">إدارة اشتراكات العملاء وباقات الإنترنت</p>
        </div>
        <button className="btn btn-primary-modern">
          <i className="fas fa-plus me-2"></i>
          إضافة اشتراك جديد
        </button>
      </div>

      <div className="card-modern">
        <div className="card-body p-0">
          <div className="table-responsive">
            <table className="table table-modern mb-0">
              <thead>
                <tr>
                  <th>العميل</th>
                  <th>الباقة</th>
                  <th>السعر</th>
                  <th>تاريخ البداية</th>
                  <th>تاريخ الانتهاء</th>
                  <th>الحالة</th>
                  <th>الإجراءات</th>
                </tr>
              </thead>
              <tbody>
                {subscriptions.map((subscription) => (
                  <tr key={subscription.id}>
                    <td>
                      <div className="d-flex align-items-center">
                        <div className="rounded-circle bg-primary bg-opacity-10 p-2 me-3">
                          <i className="fas fa-user text-primary"></i>
                        </div>
                        <div className="fw-semibold">{subscription.customerName}</div>
                      </div>
                    </td>
                    <td>{subscription.plan}</td>
                    <td>{subscription.price} ريال</td>
                    <td>{subscription.startDate}</td>
                    <td>{subscription.endDate}</td>
                    <td>
                      <span className={`status-badge ${getStatusColor(subscription.status)}`}>
                        {subscription.status}
                      </span>
                    </td>
                    <td>
                      <div className="btn-group">
                        <button className="btn btn-sm btn-outline-primary">
                          <i className="fas fa-edit"></i>
                        </button>
                        <button className="btn btn-sm btn-outline-success">
                          <i className="fas fa-sync-alt"></i>
                        </button>
                        <button className="btn btn-sm btn-outline-danger">
                          <i className="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Subscriptions
