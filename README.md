# سلام - نظام إدارة اشتراكات الإنترنت

نظام احترافي ومتقدم لإدارة اشتراكات الإنترنت مع واجهة عصرية وسهلة الاستخدام.

## المميزات

### 🎯 المميزات الأساسية
- **لوحة تحكم احترافية** مع إحصائيات شاملة
- **إدارة العملاء** مع قاعدة بيانات متكاملة
- **إدارة الاشتراكات** مع تتبع تواريخ الانتهاء
- **نظام المدفوعات** مع طرق دفع متعددة
- **التقارير والإحصائيات** المفصلة
- **نظام إشعارات** للاشتراكات المنتهية

### 🔐 الأمان والمصادقة
- **تسجيل دخول آمن** مع JWT
- **تشفير كلمات المرور** باستخدام bcrypt
- **حماية API** مع middleware للمصادقة
- **إدارة الجلسات** المتقدمة

### 🎨 التصميم والواجهة
- **تصميم عصري** مع Bootstrap 5
- **واجهة عربية** كاملة (RTL)
- **تجاوب مع جميع الأجهزة** (Responsive)
- **تأثيرات بصرية** احترافية
- **أيقونات Font Awesome** المتقدمة

### 🛠️ التقنيات المستخدمة
- **Frontend**: React.js + Vite
- **Backend**: Node.js + Express.js
- **قاعدة البيانات**: SQLite
- **المصادقة**: JWT + bcrypt
- **التصميم**: Bootstrap 5 + CSS مخصص
- **الأيقونات**: Font Awesome

## متطلبات التشغيل

- Node.js (الإصدار 16 أو أحدث)
- npm أو yarn

## التثبيت والتشغيل

### 1. تثبيت المتطلبات
```bash
npm install
```

### 2. تشغيل الخادم والتطبيق
```bash
# تشغيل الخادم والواجهة معاً
npm run dev:full

# أو تشغيلهما منفصلين:
# تشغيل الخادم
npm run server

# تشغيل الواجهة (في terminal آخر)
npm run dev
```

### 3. الوصول للتطبيق
- **الواجهة**: http://localhost:3000
- **API**: http://localhost:5000/api

## بيانات تسجيل الدخول التجريبية

```
اسم المستخدم: admin
كلمة المرور: admin123
```

## هيكل المشروع

```
سلام/
├── src/                    # ملفات الواجهة الأمامية
│   ├── components/         # مكونات React
│   │   ├── Auth/          # مكونات المصادقة
│   │   ├── Dashboard/     # لوحة التحكم
│   │   ├── Customers/     # إدارة العملاء
│   │   ├── Subscriptions/ # إدارة الاشتراكات
│   │   ├── Payments/      # إدارة المدفوعات
│   │   ├── Reports/       # التقارير
│   │   ├── Settings/      # الإعدادات
│   │   └── Layout/        # مكونات التخطيط
│   ├── context/           # React Context
│   ├── App.jsx           # المكون الرئيسي
│   ├── main.jsx          # نقطة الدخول
│   └── index.css         # الأنماط الرئيسية
├── server/               # ملفات الخادم
│   ├── index.js         # خادم Express
│   └── database.db      # قاعدة البيانات (تُنشأ تلقائياً)
├── public/              # الملفات العامة
├── package.json         # إعدادات المشروع
├── vite.config.js      # إعدادات Vite
└── index.html          # ملف HTML الرئيسي
```

## API Endpoints

### المصادقة
- `POST /api/auth/login` - تسجيل الدخول
- `GET /api/auth/me` - معلومات المستخدم الحالي

### العملاء
- `GET /api/customers` - جلب جميع العملاء
- `POST /api/customers` - إضافة عميل جديد

### الاشتراكات
- `GET /api/subscriptions` - جلب جميع الاشتراكات

### المدفوعات
- `GET /api/payments` - جلب جميع المدفوعات

### الإحصائيات
- `GET /api/dashboard/stats` - إحصائيات لوحة التحكم

## تطبيق الأندرويد

تم إنشاء تطبيق أندرويد احترافي في مجلد `mobile-app/` مع المميزات التالية:

### مميزات التطبيق المحمول:
- **واجهة عربية** مناسبة للهواتف المحمولة
- **تسجيل دخول آمن** مع حفظ الجلسة
- **لوحة تحكم محمولة** مع الإحصائيات
- **تصميم Material Design** عصري
- **إشعارات** للعمليات المختلفة

### تشغيل تطبيق الأندرويد:
```bash
cd mobile-app
npm install
npm run android  # للأندرويد
npm run ios      # للآيفون (macOS فقط)
```

### بناء APK:
```bash
cd mobile-app
npm run build-apk
```

سيتم إنشاء ملف APK في: `mobile-app/android/app/build/outputs/apk/release/app-release.apk`

## المميزات المستقبلية

- [x] **تطبيق الأندرويد** مع React Native ✅
- [ ] نظام الإشعارات المتقدم
- [ ] تقارير PDF
- [ ] نظام النسخ الاحتياطي
- [ ] API للدفع الإلكتروني
- [ ] نظام الرسائل النصية
- [ ] المزيد من شاشات التطبيق المحمول

## هيكل المشروع الكامل

```
سلام/
├── src/                    # تطبيق الويب (React)
├── server/                 # خادم API (Node.js)
├── mobile-app/            # تطبيق الأندرويد (React Native)
│   ├── App.js            # المكون الرئيسي
│   ├── package.json      # إعدادات التطبيق
│   └── android/          # ملفات الأندرويد
├── package.json          # إعدادات المشروع الرئيسي
└── README.md            # هذا الملف
```

## الدعم والمساعدة

للحصول على المساعدة أو الإبلاغ عن مشاكل، يرجى التواصل معنا.

## الترخيص

هذا المشروع مرخص تحت رخصة MIT.

---

**© 2024 سلام - نظام إدارة اشتراكات الإنترنت الشامل**
**تطبيق ويب + تطبيق أندرويد + خادم API**
