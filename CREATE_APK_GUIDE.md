# 📱 دليل إنشاء APK - تطبيق سلام

## 🎯 الطرق المتاحة لإنشاء APK

### الطريقة الأولى: استخدام Android Studio (الأسرع) ⚡

#### المتطلبات:
- Android Studio مثبت
- Java JDK 8 أو أحدث

#### الخطوات:
1. **افتح Android Studio**
2. **أنشئ مشروع جديد**:
   - اختر "Empty Activity"
   - اسم التطبيق: "سلام - إدارة الاشتراكات"
   - Package name: `com.salam.app`
   - Language: Java
   - Minimum SDK: API 21

3. **انسخ الملفات**:
   - انسخ محتوى مجلد `android-webview/` إلى مشروعك
   - استبدل الملفات الموجودة

4. **ابني APK**:
   ```
   Build → Generate Signed Bundle / APK → APK → Next
   Create new keystore أو استخدم موجود
   Build
   ```

5. **احصل على APK**:
   - المسار: `app/build/outputs/apk/release/app-release.apk`

### الطريقة الثانية: استخدام PWA Builder (أونلاين) 🌐

#### الخطوات:
1. **تأكد من تشغيل التطبيق**:
   ```bash
   npm run dev:full
   ```

2. **اذهب إلى PWA Builder**:
   - الرابط: https://www.pwabuilder.com/

3. **أدخل رابط التطبيق**:
   - URL: `http://localhost:3000`
   - أو استخدم ngrok للوصول الخارجي

4. **اختر Android Package**
5. **حمل APK**

### الطريقة الثالثة: استخدام Capacitor 🔋

#### التثبيت:
```bash
npm install -g @capacitor/cli
cd your-project
npm install @capacitor/core @capacitor/android
```

#### الإعداد:
```bash
npx cap init SalamApp com.salam.app
npx cap add android
npm run build
npx cap copy
npx cap open android
```

#### بناء APK:
```bash
cd android
./gradlew assembleRelease
```

### الطريقة الرابعة: استخدام Expo (React Native) 📱

#### التثبيت:
```bash
npm install -g @expo/cli
npx create-expo-app SalamMobile
cd SalamMobile
```

#### إعداد التطبيق:
```bash
npm install axios @react-native-async-storage/async-storage
# انسخ كود App.js من مجلد mobile-app
```

#### بناء APK:
```bash
npx expo build:android
# أو
eas build --platform android
```

## 🚀 الطريقة السريعة (موصى بها)

### استخدام أدوات أونلاين:

#### 1. AppsGeyser:
- اذهب إلى: https://appsgeyser.com/
- اختر "Website"
- أدخل: `http://your-ip:3000`
- أنشئ APK

#### 2. Website 2 APK Builder:
- اذهب إلى: https://website2apk.com/
- أدخل رابط التطبيق
- خصص الإعدادات
- حمل APK

#### 3. Appy Pie:
- اذهب إلى: https://www.appypie.com/
- اختر "App from Website"
- أدخل التفاصيل
- ابني APK

## 📋 خطوات مفصلة لـ Android Studio

### 1. إعداد المشروع:
```xml
<!-- في AndroidManifest.xml -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
```

### 2. كود MainActivity:
```java
// استخدم الكود الموجود في android-webview/app/src/main/java/
```

### 3. تخصيص الأيقونة:
- ضع أيقونة في `res/mipmap/`
- استخدم Image Asset Studio في Android Studio

### 4. بناء APK:
```bash
# في terminal Android Studio
./gradlew assembleRelease
```

## 🔧 إعدادات مهمة

### تغيير رابط الخادم:
```java
// للاستخدام المحلي
webView.loadUrl("http://********:3000");

// للاستخدام مع خادم خارجي
webView.loadUrl("https://your-domain.com");
```

### إضافة أيقونة مخصصة:
1. أنشئ أيقونة 512x512 بكسل
2. استخدم Android Studio Image Asset Studio
3. اختر "Launcher Icons (Adaptive and Legacy)"

## 📦 ملفات APK جاهزة

### للحصول على APK فوراً:

#### الطريقة الأسرع:
1. **حمل Android Studio**
2. **أنشئ مشروع جديد**
3. **انسخ ملفات `android-webview/`**
4. **ابني APK**

#### أو استخدم:
- **PWA Builder** (أونلاين)
- **AppsGeyser** (مجاني)
- **Capacitor** (احترافي)

## 🎯 نصائح مهمة

### للاستخدام المحلي:
- تأكد من تشغيل `npm run dev:full`
- استخدم `http://********:3000` في المحاكي
- استخدم `http://YOUR_IP:3000` في الجهاز الفعلي

### للنشر:
- ارفع التطبيق على خادم ويب
- استخدم HTTPS
- أضف Service Worker للعمل بدون إنترنت

### تحسين الأداء:
- فعل ضغط الملفات
- استخدم CDN
- أضف Loading Screen

## 📱 اختبار APK

### على المحاكي:
```bash
adb install app-release.apk
```

### على الجهاز الفعلي:
1. فعل "Developer Options"
2. فعل "USB Debugging"
3. ثبت APK

---

## 🎉 APK جاهز!

بعد اتباع أي من الطرق أعلاه، ستحصل على ملف APK يمكن تثبيته على أي جهاز أندرويد.

**مسار APK**: `app/build/outputs/apk/release/app-release.apk`

**حجم APK المتوقع**: 5-15 ميجابايت

**متطلبات التشغيل**: Android 5.0+ (API 21)
